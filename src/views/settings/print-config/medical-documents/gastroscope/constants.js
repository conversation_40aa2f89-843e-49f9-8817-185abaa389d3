export const EXAMPLE_DATA = {
    'id': '3802156600283971587',
    'goodsType': 3,
    'type': 2,
    'subType': 10,
    'patientId': 'ffffffff0000000034b6d66fc0a60000',
    'patientOrderId': 'ffffffff0000000034c3f948821b4000',
    'examinationApplySheetId': '3802156600283971585',
    'wardAreaId': null,
    'relationPatientOrderId': null,
    'chargeSheetId': null,
    'chargeFormItemId': null,
    'chargeSheetType': -1,
    'outpatientFormItemId': null,
    'adviceExecuteItemId': null,
    'peFormItemId': null,
    'peFormItemIds': [],
    'organPrintView': {
        'id': 'ffffffff00000000146808c695534004',
        'name': 'ABC医院',
        'contactPhone': '40008009',
        'addressProvinceName': '内蒙古',
        'addressCityName': '呼和浩特市',
        'addressDistrictName': '新城区',
        'addressDetail': '',
        'logo': 'https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/clinic-usage/ffffffff00000000146808c695534004/basic/logo_h_square_%E5%8F%A3%E8%85%94%E7%AE%A1%E5%AE%B6_6pctmlBmtqmq_ElI982vHz7Nn.jpg',
        'qrUrl': 'http://weixin.qq.com/q/02SxZ5F5JyeUD10000M07k',
        'category': '综合医院',
        'addressProvinceId': '150000',
        'addressCityId': '150100',
        'addressDistrictId': '150102',
        'hisType': 100,
        'medicalDocumentsTitle': {
            'prescription': 'ABC医院',
            'medical': 'ABC医院',
            'infusion': 'ABC医院',
            'treatment': 'ABC医院',
            'examination': 'ABC医院',
            'illnessCert': 'ABC医院',
            'inspection': null,
        },
    },
    'barCode': 19889,
    'businessType': 50,
    'orderNo': '202406030002',
    'status': 1,
    'sampleStatus': 10,
    'doctorId': '6e45706922a74966ab51e4ed1e604641',
    'doctor': {
        'id': '6e45706922a74966ab51e4ed1e604641',
        'name': '张鹏举',
        'mobile': '***********',
        'countryCode': '86',
        'status': 1,
        'shortId': '939523477594734595',
        'namePy': 'zhangpengju',
        'namePyFirst': 'ZPJ',
        'headImgUrl': 'https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/clinic-usage/ffffffff000000001ea0b8f80078c000/doctor/2_D4FOMhXaVkeq.png',
        'handSign': 'https://cis-images-dev.oss-cn-shanghai.aliyuncs.com/prescription-sign/EbTZLqepFwzQPfjkNhhEFA9FCATJuYsC_1716536781474',
        'wechatOpenIdMp': '1',
        'wechatNickName': '刘喜',
    },
    'sellerId': '6e45706922a74966ab51e4ed1e604641',
    'seller': {
        'id': '6e45706922a74966ab51e4ed1e604641',
        'name': '张鹏举',
        'mobile': '***********',
        'countryCode': '86',
        'status': 1,
        'shortId': '939523477594734595',
        'namePy': 'zhangpengju',
        'namePyFirst': 'ZPJ',
        'headImgUrl': 'https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/clinic-usage/ffffffff000000001ea0b8f80078c000/doctor/2_D4FOMhXaVkeq.png',
        'handSign': 'https://cis-images-dev.oss-cn-shanghai.aliyuncs.com/prescription-sign/EbTZLqepFwzQPfjkNhhEFA9FCATJuYsC_1716536781474',
        'wechatOpenIdMp': '1',
        'wechatNickName': '刘喜',
    },
    'doctorDepartmentId': null,
    'doctorDepartmentName': null,
    'sellerDepartmentId': null,
    'sellerDepartmentName': null,
    'executeDepartmentId': '',
    'executeDepartmentName': null,
    'attachments': [],
    'remark': null,
    'itemsValue': null,
    'deviceData': null,
    'preItemsValue': null,
    'examinationHistories': null,
    'created': '2024-06-03T01:15:48Z',
    'orderByDate': '2024-06-03T01:15:48Z',
    'lastModifiedBy': '6e45706922a74966ab51e4ed1e604641',
    'patientOrderNumber': '202406030002',
    'wardAreaName': null,
    'bedNumber': null,
    'testerId': '6e45706922a74966ab51e4ed1e604641',
    'testerName': '张鹏举',
    'tester': {
        'id': '6e45706922a74966ab51e4ed1e604641',
        'name': '张鹏举',
        'mobile': '***********',
        'countryCode': '86',
        'status': 1,
        'shortId': '939523477594734595',
        'namePy': 'zhangpengju',
        'namePyFirst': 'ZPJ',
        'headImgUrl': 'https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/clinic-usage/ffffffff000000001ea0b8f80078c000/doctor/2_D4FOMhXaVkeq.png',
        'handSign': 'https://cis-images-dev.oss-cn-shanghai.aliyuncs.com/prescription-sign/EbTZLqepFwzQPfjkNhhEFA9FCATJuYsC_1716536781474',
        'wechatOpenIdMp': '1',
        'wechatNickName': '刘喜',
    },
    'testTime': '2024-06-03T01:59:00Z',
    'sampleType': null,
    'checkerId': '6e45706922a74966ab51e4ed1e604641',
    'checkerName': '张鹏举',
    'checker': {
        'id': '6e45706922a74966ab51e4ed1e604641',
        'name': '张鹏举',
        'mobile': '***********',
        'countryCode': '86',
        'status': 1,
        'shortId': '939523477594734595',
        'namePy': 'zhangpengju',
        'namePyFirst': 'ZPJ',
        'headImgUrl': 'https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/clinic-usage/ffffffff000000001ea0b8f80078c000/doctor/2_D4FOMhXaVkeq.png',
        'handSign': 'https://cis-images-dev.oss-cn-shanghai.aliyuncs.com/prescription-sign/EbTZLqepFwzQPfjkNhhEFA9FCATJuYsC_1716536781474',
        'wechatOpenIdMp': '1',
        'wechatNickName': '刘喜',
    },
    'checkTime': '2024-06-03T02:52:00Z',
    'reportTime': '2024-06-03T02:52:14Z',
    'chargeFormItemStatus': 0,
    'chargeFormItemOnceFee': 0,
    'canExecute': 1,
    'diagnosis': null,
    'patient': {
        'id': 'ffffffff0000000034b6d66fc0a60000',
        'name': '李强',
        'namePy': 'liqiang',
        'namePyFirst': 'LQ',
        'mobile': '15214580896',
        'countryCode': '86',
        'sex': '男',
        'birthday': '1979-05-03',
        'age': {
            'year': 45,
            'month': 1,
            'day': 0,
        },
        'isMember': 0,
        'idCard': '******************',
        'marital': null,
        'weight': null,
        'importFlag': 0,
        'ethnicity': '',
        'nationality': null,
        'contactName': '18979798080',
        'contactRelation': '本人',
        'contactMobile': '18979798080',
        'sn': '001652',
        'remark': '',
        'profession': '职员',
        'company': '琼海忠锐农业开发有限公司',
        'companyMobile': null,
        'blockFlag': 0,
        'address': {
            'addressCityId': null,
            'addressCityName': null,
            'addressDetail': '',
            'addressDistrictId': null,
            'addressDistrictName': null,
            'addressGeo': null,
            'addressProvinceId': null,
            'addressProvinceName': null,
            'addressPostcode': null,
            'fullAddress': '',
        },
        'familyMobile': null,
        'tags': null,
        'activeDate': '2024-05-31T06:26:09.000+00:00',
        'activeClinicId': 'ffffffff00000000146808c695534004',
        'lastOutpatientDate': '2024-05-30T05:51:46.000+00:00',
        'lastOutpatientClinicId': 'ffffffff00000000146808c695534004',
        'wxOpenId': null,
        'unionId': null,
        'wxUserId': null,
        'wxNickName': null,
        'wxHeadImgUrl': null,
        'isWxMainPatient': 0,
        'wxBindStatus': 0,
        'allergicHistory': '1',
    },
    'registrationFormItem': null,
    'examinationSheetReport': {
        'id': 'ffffffff0000000034c3f94884554000',
        'goodsType': 3,
        'type': 2,
        'subType': 10,
        'deviceType': 10,
        'imageFiles': [
            {
                'url': 'https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/clinic-usage/ffffffff00000000146808c695534004/inspect/wcj_GTV6aSVeEWOP.png',
                'fileName': '',
                'fileSize': null,
            },
            {
                'url': 'https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/clinic-usage/ffffffff00000000146808c695534004/inspect/wcj_GTV6aSVeEWOP.png',
                'fileName': '',
                'fileSize': null,
            },
            {
                'url': 'https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/clinic-usage/ffffffff00000000146808c695534004/inspect/wcj_GTV6aSVeEWOP.png',
                'fileName': '',
                'fileSize': null,
            },
            {
                'url': 'https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/clinic-usage/ffffffff00000000146808c695534004/inspect/wcj_GTV6aSVeEWOP.png',
                'fileName': '',
                'fileSize': null,
            },
        ],
        'method': null,
        'videoDescription': 'asda',
        'resultInfo': null,
        'advice': '',
        'suggestion': 'asdas',
        'diagnosisFlag': 0,
        'versionFlag': 0,
        'recordDoctorId': '6e45706922a74966ab51e4ed1e604641',
        'recordDoctor': {
            'id': '6e45706922a74966ab51e4ed1e604641',
            'name': '张鹏举',
            'mobile': '***********',
            'countryCode': '86',
            'status': 1,
            'shortId': '939523477594734595',
            'namePy': 'zhangpengju',
            'namePyFirst': 'ZPJ',
            'headImgUrl': 'https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/clinic-usage/ffffffff000000001ea0b8f80078c000/doctor/2_D4FOMhXaVkeq.png',
            'handSign': 'https://cis-images-dev.oss-cn-shanghai.aliyuncs.com/prescription-sign/EbTZLqepFwzQPfjkNhhEFA9FCATJuYsC_1716536781474',
            'wechatOpenIdMp': '1',
            'wechatNickName': '刘喜',
        },
        'consultationDoctorId': null,
        'principalDoctorId': null,
        'principalDoctorSign': null,
        'checkerId': '6e45706922a74966ab51e4ed1e604641',
        'examinationName': '胃肠镜',
        'inspectionSite': '阿斯达',
        'deviceModelDesc': '胃镜',
        'diagnosisEntryItems': [
            {
                'id': '3802158042319241216',
                'diagnosisEntryId': '3800869938676219904',
                'type': 2,
                'deviceType': 10,
                'name': '内窥镜检查异常02',
                'abnormalFlag': 10,
            },
            {
                'id': '3802158042319241217',
                'type': 2,
                'deviceType': 10,
                'name': '111',
                'abnormalFlag': 0,
                'sort': 1,
            },
        ],
        'recordDoctorName': '张鹏举',
        'consultationDoctorName': null,
        'principalDoctorName': null,
        'principalDoctorMobile': null,
    },
    'samplePipe': null,
    'deviceModelId': '3793494177410826240',
    'deviceId': '3793494212934107136',
    'deviceRoomId': '3793494257134927872',
    'deviceView': {
        'id': '3793494177410826240',
        'deviceModelId': '3793494177410826240',
        'name': '胃肠镜设备',
        'model': 'wcg',
        'deviceUuid': '检查设备',
        'manufacture': '检查设备',
        'goodsType': 3,
        'goodsSubType': 2,
        'goodsExtendSpec': '10',
        'deviceType': 10,
        'deviceTypeName': '胃肠镜',
        'usageType': 0,
        'usageTypeName': '未知分类',
        'innerFlag': 0,
        'sampleNoRule': null,
        'connectStatus': 0,
        'deviceModelStatus': 0,
        'deviceId': '3793494212934107136',
        'deviceShortId': '000043',
        'deviceStatus': 10,
        'deviceStatusName': '使用中',
        'deviceRoomId': '3793494257134927872',
        'deviceRoomName': '胃肠镜机房',
        'department': {
            'id': 'ffffffff0000000034692fb595df0000',
            'name': '妇产科',
            'departmentAddress': '4楼',
            'tagId': 'ffffffff0000000034bf914c09b2c000',
            'type': 1,
            'isDefault': 0,
            'mobile': '',
            'beds': null,
            'customId': 'ZN0020',
            'isClinical': 5,
            'mainMedical': 'f7220052-a44a-11e9-99a1-acde48001122',
            'mainMedicalName': '全科医疗科',
            'mainMedicalCode': '02',
            'status': 0,
        },
        'extendInfos': {
            'dcm4cheeConfig': {
                'enablePinyin': null,
                'characterSet': null,
            },
        },
    },
    'examinationApplySheetView': {
        'id': '3802156600283971585',
        'chainId': 'ffffffff00000000146808c695534000',
        'clinicId': 'ffffffff00000000146808c695534004',
        'doctorId': '6e45706922a74966ab51e4ed1e604641',
        'departmentId': '',
        'patientId': 'ffffffff0000000034b6d66fc0a60000',
        'patientOrderId': 'ffffffff0000000034c3f948821b4000',
        'registrationFormItemId': 'ffffffff0000000034c3f948823bc002',
        'deviceId': '3793494212934107136',
        'deviceRoomId': '3793494257134927872',
        'no': 'GI2406030001',
        'businessType': 0,
        'goodsType': 3,
        'type': 2,
        'subType': 10,
        'deviceType': 10,
        'chiefComplaint': null,
        'presentHistory': null,
        'physicalExamination': null,
        'diagnosisInfos': [],
        'purpose': null,
        'planExecuteDate': '2024-06-03',
        'created': '2024-06-03T01:15:49Z',
        'status': 20,
        'patient': null,
        'dcm4cheeView': null,
        'doctorName': null,
    },
    'location': null,
    'peSheetSimpleView': null,
    'deviceType': 10,
    'isMerge': 0,
    'importFlag': 0,
    'updateItemsValueFlag': 1,
    'reportInvalidFlag': 1,
    'innerFlag': 0,
    'goodsSubType': 2,
    'departmentId': null,
    'deviceStatus': 10,
    'examinationApplySheetNo': 'GI2406030001',
    'doctorName': '张鹏举',
    'sellerName': '张鹏举',
    'clinicPrintName': 'ABC医院',
    'deviceName': '胃肠镜设备',
    'lastModifiedMillsTime': '1717383134000',
    'departmentName': null,
    'lastModifiedTime': '2024-06-03T02:52:14Z',
    'name': '胃肠镜',
    'modifier': {
        'id': '6e45706922a74966ab51e4ed1e604641',
        'name': '张鹏举',
        'mobile': '***********',
        'countryCode': '86',
        'status': 1,
        'shortId': '939523477594734595',
        'namePy': 'zhangpengju',
        'namePyFirst': 'ZPJ',
        'headImgUrl': 'https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/clinic-usage/ffffffff000000001ea0b8f80078c000/doctor/2_D4FOMhXaVkeq.png',
        'handSign': 'https://cis-images-dev.oss-cn-shanghai.aliyuncs.com/prescription-sign/EbTZLqepFwzQPfjkNhhEFA9FCATJuYsC_1716536781474',
        'wechatOpenIdMp': '1',
        'wechatNickName': '刘喜',
    },
    'extendDiagnosisInfos': null,
    'modifierName': '张鹏举',
};
