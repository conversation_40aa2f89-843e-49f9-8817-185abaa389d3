<template>
    <biz-setting-layout>
        <biz-setting-content>
            <abc-form ref="printForm" item-no-margin>
                <biz-setting-form :label-width="84" divider-full-screen>
                    <biz-setting-form-header>
                        <medical-technology-report-tab v-if="from === 2">
                        </medical-technology-report-tab>

                        <report-tab v-else></report-tab>
                    </biz-setting-form-header>

                    <biz-setting-form-group title="彩超报告前记">
                        <biz-setting-form-item label="抬头名称" label-line-height-size="medium">
                            <abc-flex :gap="8">
                                <abc-form-item required class="print-form-item" :validate-event="validateName">
                                    <title-setting v-model="postData.header.title" :max-length="titleMaxLength * 2"></title-setting>
                                </abc-form-item>

                                <abc-space>
                                    <abc-button
                                        v-if="!isShowSubtitle"
                                        variant="text"
                                        size="small"
                                        @click="isShowSubtitle = true"
                                    >
                                        加一行
                                    </abc-button>

                                    <abc-button variant="text" size="small" @click="handleSetAllDocuments">
                                        应用至全部
                                    </abc-button>
                                </abc-space>
                            </abc-flex>

                            <abc-flex v-if="isShowSubtitle" :gap="8">
                                <abc-form-item class="print-form-item">
                                    <title-setting v-model="postData.header.subtitle" :max-length="titleMaxLength * 2"></title-setting>
                                </abc-form-item>

                                <abc-button
                                    variant="text"
                                    theme="danger"
                                    size="small"
                                    @click="deleteSubTitle"
                                >
                                    删除
                                </abc-button>
                            </abc-flex>
                        </biz-setting-form-item>

                        <biz-setting-form-item label="抬头颜色">
                            <abc-radio-group v-model="postData.header.titleColor">
                                <abc-flex gap="12" wrap="wrap">
                                    <abc-radio :label="0" class="checkbox-no-margin">
                                        黑色
                                    </abc-radio>
                                    <abc-radio :label="1" class="checkbox-no-margin">
                                        蓝色
                                    </abc-radio>
                                    <abc-radio :label="2" class="checkbox-no-margin">
                                        红色
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>

                        <biz-setting-form-item label="抬头副标题" label-line-height-size="medium">
                            <abc-flex :gap="8">
                                <abc-form-item required class="print-form-item" :validate-event="validateName">
                                    <title-setting v-model="postData.header.assistantTitle" :max-length="titleMaxLength * 2"></title-setting>
                                </abc-form-item>

                                <abc-checkbox v-model="postData.header.assistantTitleUseProductName" type="number">
                                    使用项目名称
                                </abc-checkbox>
                            </abc-flex>
                        </biz-setting-form-item>

                        <biz-setting-form-item label="抬头信息">
                            <abc-flex :gap="12">
                                <abc-checkbox v-model="postData.header.logo" type="number" class="checkbox-no-margin">
                                    机构Logo
                                </abc-checkbox>
                                <abc-checkbox v-model="postData.header.inspectionDevice" type="number" class="checkbox-no-margin">
                                    检查设备
                                </abc-checkbox>
                                <abc-checkbox v-model="postData.header.barcode" type="number" class="checkbox-no-margin">
                                    检查单号条码
                                </abc-checkbox>
                            </abc-flex>
                        </biz-setting-form-item>
                    </biz-setting-form-group>

                    <biz-setting-form-group title="彩超报告中记">
                        <biz-setting-form-item label="基础信息">
                            <abc-flex :gap="12" wrap="wrap">
                                <abc-checkbox
                                    v-model="postData.content.patientName"
                                    type="number"
                                    disabled
                                    class="checkbox-no-margin"
                                >
                                    姓名
                                </abc-checkbox>

                                <abc-checkbox
                                    v-model="postData.content.patientSex"
                                    type="number"
                                    disabled
                                    class="checkbox-no-margin"
                                >
                                    性别
                                </abc-checkbox>

                                <abc-checkbox
                                    v-model="postData.content.patientAge"
                                    type="number"
                                    disabled
                                    class="checkbox-no-margin"
                                >
                                    年龄
                                </abc-checkbox>

                                <abc-checkbox v-model="postData.content.patientOrderNo" type="number" class="checkbox-no-margin">
                                    门诊/住院/体检号
                                </abc-checkbox>

                                <abc-checkbox
                                    v-model="postData.content.inspectionOrderNo"
                                    type="number"
                                    class="checkbox-no-margin"
                                >
                                    检查单号
                                </abc-checkbox>

                                <abc-checkbox
                                    v-model="postData.content.inspectionApplyNo"
                                    type="number"
                                    class="checkbox-no-margin"
                                >
                                    检查编号
                                </abc-checkbox>

                                <abc-checkbox v-model="postData.content.applyDepartment" type="number" class="checkbox-no-margin">
                                    申请科室
                                </abc-checkbox>

                                <abc-checkbox v-model="postData.content.applyDoctor" type="number" class="checkbox-no-margin">
                                    申请医生
                                </abc-checkbox>

                                <abc-checkbox v-model="postData.content.applyDate" type="number" class="checkbox-no-margin">
                                    申请日期
                                </abc-checkbox>

                                <abc-checkbox v-model="postData.content.clinicalDiagnosis" type="number" class="checkbox-no-margin">
                                    临床诊断
                                </abc-checkbox>

                                <abc-checkbox
                                    v-model="postData.content.inspectionSite"
                                    type="number"
                                    disabled
                                    class="checkbox-no-margin"
                                >
                                    检查部位
                                </abc-checkbox>
                            </abc-flex>
                        </biz-setting-form-item>

                        <biz-setting-form-item label="检查标题" label-line-height-size="medium">
                            <abc-form-item required :validate-event="validateName">
                                <title-setting v-model="postData.content.inspectionHeader" :max-length="titleMaxLength * 2"></title-setting>
                            </abc-form-item>
                        </biz-setting-form-item>

                        <biz-setting-form-item label="结论标题" label-line-height-size="medium">
                            <abc-form-item required :validate-event="validateName">
                                <title-setting v-model="postData.content.conclusionHeader" :max-length="titleMaxLength * 2"></title-setting>
                            </abc-form-item>
                        </biz-setting-form-item>
                    </biz-setting-form-group>

                    <biz-setting-form-group title="彩超报告后记">
                        <biz-setting-form-item label="基础信息">
                            <abc-flex :gap="12" wrap="wrap">
                                <abc-checkbox
                                    v-model="postData.footer.operateDoctor"
                                    type="number"
                                    class="checkbox-no-margin"
                                >
                                    报告医师
                                </abc-checkbox>
                                <abc-checkbox
                                    v-model="postData.footer.checkDoctor"
                                    type="number"
                                    class="checkbox-no-margin"
                                >
                                    审核医师
                                </abc-checkbox>
                                <abc-checkbox
                                    v-model="postData.footer.recordDoctor"
                                    type="number"
                                    disabled
                                    class="checkbox-no-margin"
                                >
                                    记录医师
                                </abc-checkbox>
                                <abc-checkbox
                                    v-model="postData.footer.consultationDoctor"
                                    type="number"
                                    class="checkbox-no-margin"
                                >
                                    会诊医师
                                </abc-checkbox>
                                <abc-checkbox v-model="postData.footer.inspectionTime" type="number" class="checkbox-no-margin">
                                    检查时间
                                </abc-checkbox>
                                <abc-checkbox v-model="postData.footer.auditTime" type="number" class="checkbox-no-margin">
                                    审核时间
                                </abc-checkbox>
                                <abc-checkbox v-model="postData.footer.reportTime" type="number" class="checkbox-no-margin">
                                    报告时间
                                </abc-checkbox>
                            </abc-flex>
                        </biz-setting-form-item>

                        <biz-setting-form-item v-if="postData.footer.operateDoctor" label="报告医师签名">
                            <abc-radio-group v-model="postData.footer.operateDoctorSignature">
                                <abc-flex :gap="12" wrap="wrap">
                                    <abc-radio :label="0" class="checkbox-no-margin">
                                        电脑签名
                                    </abc-radio>
                                    <abc-radio :label="1" class="checkbox-no-margin">
                                        手写电子签
                                    </abc-radio>
                                    <abc-radio :label="2" class="checkbox-no-margin">
                                        不签名
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>

                        <biz-setting-form-item v-if="postData.footer.checkDoctor" label="审核医师签名">
                            <abc-radio-group v-model="postData.footer.checkDoctorSignature">
                                <abc-flex :gap="12" wrap="wrap">
                                    <abc-radio :label="0" class="checkbox-no-margin">
                                        电脑签名
                                    </abc-radio>
                                    <abc-radio :label="1" class="checkbox-no-margin">
                                        手写电子签
                                    </abc-radio>
                                    <abc-radio :label="2" class="checkbox-no-margin">
                                        不签名
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>

                        <biz-setting-form-item v-if="postData.footer.recordDoctor" label="记录医师签名">
                            <abc-radio-group v-model="postData.footer.recordDoctorSignature">
                                <abc-flex :gap="12" wrap="wrap">
                                    <abc-radio :label="0" class="checkbox-no-margin">
                                        电脑签名
                                    </abc-radio>
                                    <abc-radio :label="1" class="checkbox-no-margin">
                                        手写电子签
                                    </abc-radio>
                                    <abc-radio :label="2" class="checkbox-no-margin">
                                        不签名
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>

                        <biz-setting-form-item v-if="postData.footer.consultationDoctor" label="会诊医师签名">
                            <abc-radio-group v-model="postData.footer.consultationDoctorSignature">
                                <abc-flex :gap="12" wrap="wrap">
                                    <abc-radio :label="0" class="checkbox-no-margin">
                                        电脑签名
                                    </abc-radio>
                                    <abc-radio :label="1" class="checkbox-no-margin">
                                        手写电子签
                                    </abc-radio>
                                    <abc-radio :label="2" class="checkbox-no-margin">
                                        不签名
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>

                        <biz-setting-form-item label="备注信息" label-line-height-size="medium">
                            <abc-textarea
                                v-model="postData.footer.remark"
                                :height="70"
                                :width="486"
                                placeholder="输入备注内容"
                                maxlength="96"
                            >
                            </abc-textarea>
                        </biz-setting-form-item>
                    </biz-setting-form-group>
                </biz-setting-form>
            </abc-form>

            <template #footer>
                <biz-setting-footer>
                    <abc-space>
                        <abc-button :disabled="disabled" :loading="btnLoading" @click="handleSave">
                            保存
                        </abc-button>
                        <abc-button variant="ghost" @click="handleReset">
                            恢复默认
                        </abc-button>
                    </abc-space>
                </biz-setting-footer>
            </template>
        </biz-setting-content>

        <biz-setting-sidebar>
            <preview-layout>
                <template slot="previewTab">
                    <div class="preview-tab-item preview-tab-item__active">
                        彩超报告
                    </div>
                </template>
                <div slot="previewHtml" ref="previewMountPoint"></div>
            </preview-layout>
        </biz-setting-sidebar>
    </biz-setting-layout>
</template>

<script>
    import PreviewLayout from '../../components/preview-layout.vue';
    import TitleSetting from 'views/settings/print-config/components/title-setting';
    import clone from 'utils/clone.js';
    import { isEqual } from 'utils/lodash.js';

    import { validateName } from 'views/inventory/goods/utils.js';
    import { mapGetters } from 'vuex';
    import PrintAPI from 'api/print.js';

    import MedicalDocumentsMixins from '../mixins/index.js';
    import MixinPrint from 'views/settings/print-config/medical-documents/mixin-print';
    import store from '@/store';
    import { EXAMPLE_DATA } from './constants';
    import { getLengthWithFullCharacter } from 'views/settings/print-config/utils';
    import {
        BizSettingForm,
        BizSettingFormGroup, BizSettingFormHeader,
        BizSettingFormItem,
    } from '@/components-composite/setting-form/index.js';

    import {
        BizSettingLayout,
        BizSettingSidebar,
        BizSettingContent,
        BizSettingFooter,
    } from '@/components-composite/setting-form-layout/index.js';
    import ReportTab from 'views/settings/print-config/components/report-tab/index.vue';
    import MedicalTechnologyReportTab from '@/views-hospital/settings-common/frames/medical-technology-print-config/components/report-tab/report-tab.vue';

    const ResetData = {
        'header': { // 处方前记
            'title': '', // 抬头名称 字符串必填
            'subtitle': '', // 副抬头名称 可选值
            'titleColor': 0,//抬头颜色
            'assistantTitle': '彩色多普勒超声报告', // 副抬头名称 可使用项目名称
            'assistantTitleUseProductName': 0,//项目副抬头名称 可使用项目名称
            'logo': 0, // Logo
            'inspectionDevice': 1, // 检查设备'
            'barcode': 0,// 检查单号条码
        },
        'content': {
            'patientName': 1, // 姓名
            'patientSex': 1,//性别
            'patientAge': 1,//年龄
            'patientOrderNo': 1,// 门诊/住院/体检号
            'inspectionOrderNo': 1,//检查单号
            'inspectionApplyNo': 0, // 检查编号
            'applyDepartment': 1,//申请科室
            'applyDoctor': 1,//申请医生
            'applyDate': 1,//申请日期
            'clinicalDiagnosis': 1,//临床诊断
            'inspectionSite': 1,//检查部位
            'inspectionHeader': '超声所见',//检查标题
            'conclusionHeader': '诊断意见',//结论标题
        },
        'footer': {
            'operateDoctor': 1,//报告医师
            'checkDoctor': 0, //审核医师
            'recordDoctor': 1,//记录医师
            'inspectionTime': 1, // 检查时间
            'auditTime': 1, // 审核时间
            'reportTime': 1, // 报告时间
            'remark': '本检查结果仅反应受检查者当时的情况，仅供临床医生诊断时参考。', // 页尾备注
            'operateDoctorSignature': 0, // 报告医师签名
            'checkDoctorSignature': 0, //审核医师签名
            'recordDoctorSignature': 0, // 记录医师签名
            'consultationDoctor': 1, // 会诊医师
            'consultationDoctorSignature': 0, // 会诊医师签名
        },
    };
    export default {
        name: 'CDUSPrintConfig',
        components: {
            ReportTab,
            BizSettingFormHeader,
            PreviewLayout,
            TitleSetting,
            BizSettingForm,
            BizSettingFormGroup,
            BizSettingFormItem,
            BizSettingLayout,
            BizSettingSidebar,
            BizSettingContent,
            BizSettingFooter,
            MedicalTechnologyReportTab,
        },
        mixins: [MedicalDocumentsMixins, MixinPrint],
        props: {
            from: {
                type: Number,
                default: 1,
            },
        },
        data() {
            return {
                postData: clone(ResetData),
                btnLoading: false,
                templateStr: '',
                isShowSubtitle: false,
                titleMaxLength: 15, // 标题最大长度
                postDataCache: null,
            };
        },
        computed: {
            ...mapGetters([
                'printMedicalDocumentsConfig',
                'currentClinic',
            ]),
            disabled() {
                if (!this.postDataCache) return true;
                return isEqual(this.postData, this.postDataCache);
            },
            currentExampleData() {
                return EXAMPLE_DATA;
            },
        },

        watch: {
            'printMedicalDocumentsConfig.cdusReport': {
                handler() {
                    if (this.printMedicalDocumentsConfig.cdusReport) {
                        const cachePostData = clone(this.printMedicalDocumentsConfig.cdusReport);
                        // 判断抬头是否超过12个字符(需分区全角/半角),将超过部分拆分到副抬头
                        const {
                            fullCharacterLength, splitLength,
                        } = getLengthWithFullCharacter(cachePostData.header.title, this.titleMaxLength);
                        if (fullCharacterLength > this.titleMaxLength) {
                            const cacheTitle = cachePostData.header.title;
                            cachePostData.header.title = cacheTitle.slice(0, splitLength);
                            cachePostData.header.subtitle = cacheTitle.slice(splitLength);
                            this.postData = cachePostData;
                            this.updateTitleWithParams('cdusReport');
                        } else {
                            this.postData = cachePostData;
                        }
                        if (!this.postData.header.title) {
                            const cacheClinicName = this.currentClinic.clinicName;
                            const {
                                fullCharacterLength: fullClinicCharacterLength, splitLength: splitClinicLength,
                            } = getLengthWithFullCharacter(cacheClinicName, this.titleMaxLength);
                            if (fullClinicCharacterLength > this.titleMaxLength) {
                                this.postData.header.title = cacheClinicName.slice(0, splitClinicLength);
                                this.postData.header.subtitle = cacheClinicName.slice(splitClinicLength);
                            } else {
                                this.postData.header.title = cacheClinicName;
                            }
                        }
                        this.isShowSubtitle = !!this.postData.header.subtitle;

                        this.postDataCache = clone(this.postData);
                    }
                },
                immediate: true,
                deep: true,
            },
        },
        methods: {
            validateName,
            instanceGlobalConfigHandler(newValue) {
                const newInstanceGlobalConfig = clone(store.getters.printGlobalConfig);
                newInstanceGlobalConfig.medicalDocuments.cdusReport = newValue;
                return newInstanceGlobalConfig;
            },
            getCurrentTemplate() {
                return window.AbcPackages.AbcTemplates.hospitalInspect;
            },
            handleSave() {
                this.$refs.printForm.validate(async (val) => {
                    if (val) {
                        await this.updatePrintConfig();
                    }
                });
            },
            async updatePrintConfig() {
                try {
                    this.btnLoading = true;
                    const { data } = await PrintAPI.updatePrintConfig('clinic', 'print.medicalDocuments.cdusReport', {
                        cdusReport: this.postData,
                    });
                    this.$store.commit('SET_PRINT_MEDICAL_DOCUMENTS_SUB_CONFIG', {
                        key: 'cdusReport',
                        data: data.cdusReport,
                    });
                    this.btnLoading = false;
                    this.$Toast({
                        message: '保存成功',
                        type: 'success',
                    });
                } catch (e) {
                    this.btnLoading = false;
                }
            },
            handleReset() {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '是否确认将当前全部设置恢复为系统默认设置？',
                    onConfirm: () => {
                        this.postData = clone(ResetData);

                        // 如果门店名称超过15(全角)/30(半角)个字,则进行拆分
                        const cacheClinicName = this.currentClinic.clinicName;
                        const {
                            fullCharacterLength: fullClinicCharacterLength, splitLength: splitClinicLength,
                        } = getLengthWithFullCharacter(cacheClinicName, this.titleMaxLength);
                        if (fullClinicCharacterLength > this.titleMaxLength) {
                            this.postData.header.title = cacheClinicName.slice(0, splitClinicLength);
                            this.postData.header.subtitle = cacheClinicName.slice(splitClinicLength);
                        } else {
                            this.postData.header.title = cacheClinicName;
                        }
                    },
                });

            },
            handleSetAllDocuments() {
                this.$refs.printForm.validate((val) => {
                    if (val) {
                        let content = `是否确认将全部医疗文书抬头名称统一修改为“${this.postData.header.title}”？`;
                        if (this.postData.header.subtitle) {
                            content = `是否确认将全部医疗文书抬头名称统一修改为“${this.postData.header.title}”和“${this.postData.header.subtitle}”？`;
                        }
                        // 设置为全部医疗文书
                        this.$confirm({
                            type: 'warn',
                            title: '提示',
                            content,
                            onConfirm: async () => {
                                await this.updateTitle();
                                this.$Toast({
                                    message: '设置成功',
                                    type: 'success',
                                });
                            },
                        });
                    }
                });
            },
            /**
             * 删除副抬头
             */
            deleteSubTitle() {
                this.isShowSubtitle = false;
                this.postData.header.subtitle = '';
            },
        },
    };
</script>

<style lang="scss">
.cdfi-print-config-wrapper {
    .checkbox-no-margin {
        margin: 0 !important;
    }
}
</style>
