export const EXAMPLE_DATA = {
    'id': 'ffffffff0000000034bc679944374000',
    'goodsType': 3,
    'type': 2,
    'subType': 10,
    'patientId': 'ffffffff00000000348b9ddb20138000',
    'patientOrderId': 'ffffffff0000000034bc67994216c000',
    'examinationApplySheetId': '3800026093565984769',
    'wardAreaId': null,
    'relationPatientOrderId': null,
    'chargeSheetId': null,
    'chargeFormItemId': null,
    'chargeSheetType': -1,
    'outpatientFormItemId': null,
    'adviceExecuteItemId': null,
    'peFormItemId': null,
    'peFormItemIds': null,
    'organPrintView': {
        'id': 'ffffffff00000000146808c695534004',
        'name': 'ABC医院',
        'contactPhone': '40008009',
        'addressProvinceName': '内蒙古',
        'addressCityName': '呼和浩特市',
        'addressDistrictName': '新城区',
        'addressDetail': '',
        'logo': 'https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/clinic-usage/ffffffff00000000146808c695534004/basic/logo_h_square_%E5%8F%A3%E8%85%94%E7%AE%A1%E5%AE%B6_6pctmlBmtqmq_ElI982vHz7Nn.jpg',
        'qrUrl': 'http://weixin.qq.com/q/02SxZ5F5JyeUD10000M07k',
        'category': '综合医院',
        'addressProvinceId': '150000',
        'addressCityId': '150100',
        'addressDistrictId': '150102',
        'hisType': 100,
        'medicalDocumentsTitle': {
            'prescription': 'ABC医院',
            'medical': 'ABC医院',
            'infusion': 'ABC医院',
            'treatment': 'ABC医院',
            'examination': 'ABC医院',
            'illnessCert': 'ABC医院',
            'inspection': null,
        },
    },
    'barCode': 19180,
    'businessType': 50,
    'orderNo': '202406030002',
    'sampleNo': '202406030002',
    'status': 1,
    'sampleStatus': 10,
    'doctorId': '',
    'sellerId': null,
    'doctorDepartmentId': null,
    'doctorDepartmentName': null,
    'sellerDepartmentId': null,
    'sellerDepartmentName': null,
    'executeDepartmentId': 'ffffffff0000000034a2113012d24000',
    'executeDepartmentName': null,
    'attachments': [],
    'remark': null,
    'itemsValue': null,
    'deviceData': null,
    'preItemsValue': null,
    'examinationHistories': null,
    'created': '2024-04-18T02:56:11Z',
    'orderByDate': '2024-04-18T02:56:11Z',
    'lastModifiedBy': '6e45706922a74966ab51e4ed1e604641',
    'patientOrderNumber': '202406030002',
    'wardAreaName': null,
    'bedNumber': null,
    'testerId': '6e45706922a74966ab51e4ed1e604641',
    'testerName': '于蒙',
    'tester': {
        'id': '6e45706922a74966ab51e4ed1e604641',
        'name': '宁铁桥',
        'mobile': '***********',
        'countryCode': '86',
        'status': 1,
        'shortId': '939523477594734595',
        'namePy': 'yumeng',
        'namePyFirst': 'YM',
        'headImgUrl': 'https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/clinic-usage/ffffffff000000001ea0b8f80078c000/doctor/2_D4FOMhXaVkeq.png',
        'handSign': 'https://cis-images-dev.oss-cn-shanghai.aliyuncs.com/prescription-sign/pjBynQ8Gypg2rMNtijUcfYvLsCgBoFqt_1704969123981',
        'wechatOpenIdMp': '1',
        'wechatNickName': '刘喜',
    },
    'testTime': '2024-04-18T02:56:00Z',
    'sampleType': null,
    'checkerId': '6e45706922a74966ab51e4ed1e604641',
    'checkerName': '于蒙',
    'checker': {
        'id': '6e45706922a74966ab51e4ed1e604641',
        'name': '宁铁桥',
        'mobile': '***********',
        'countryCode': '86',
        'status': 1,
        'shortId': '939523477594734595',
        'namePy': 'yumeng',
        'namePyFirst': 'YM',
        'headImgUrl': 'https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/clinic-usage/ffffffff000000001ea0b8f80078c000/doctor/2_D4FOMhXaVkeq.png',
        'handSign': 'https://cis-images-dev.oss-cn-shanghai.aliyuncs.com/prescription-sign/pjBynQ8Gypg2rMNtijUcfYvLsCgBoFqt_1704969123981',
        'wechatOpenIdMp': '1',
        'wechatNickName': '刘喜',
    },
    'checkTime': '2024-04-18T02:56:00Z',
    'reportTime': '2024-04-18T09:09:00Z',
    'chargeFormItemStatus': 0,
    'canExecute': 1,
    'diagnosis': null,
    'patient': {
        'id': 'ffffffff00000000348b9ddb20138000',
        'name': '张雪峰',
        'namePy': 'zhangxuefeng',
        'namePyFirst': 'ZXF',
        'mobile': '',
        'countryCode': '86',
        'sex': '男',
        'birthday': '1978-06-27',
        'age': {
            'year': 45,
            'month': 9,
            'day': 21,
        },
        'isMember': 0,
        'idCard': '',
        'marital': null,
        'weight': null,
        'importFlag': 0,
        'ethnicity': '',
        'nationality': null,
        'contactName': null,
        'contactRelation': null,
        'contactMobile': null,
        'sn': '001006',
        'remark': '',
        'profession': '',
        'company': '',
        'companyMobile': null,
        'blockFlag': 0,
        'address': {
            'addressCityId': null,
            'addressCityName': null,
            'addressDetail': '',
            'addressDistrictId': null,
            'addressDistrictName': null,
            'addressGeo': null,
            'addressProvinceId': null,
            'addressProvinceName': null,
            'addressPostcode': null,
            'fullAddress': '',
        },
        'familyMobile': null,
        'tags': null,
        'activeDate': '2023-09-15T05:33:46.000+00:00',
        'activeClinicId': 'ffffffff00000000146808c695534004',
        'lastOutpatientDate': '2023-09-15T05:33:45.000+00:00',
        'lastOutpatientClinicId': 'ffffffff00000000146808c695534004',
        'wxOpenId': null,
        'unionId': null,
        'wxUserId': null,
        'wxNickName': null,
        'wxHeadImgUrl': null,
        'isWxMainPatient': 0,
        'wxBindStatus': 0,
        'patientSource': {
            'parentId': null,
            'parentName': null,
            'id': '11413126450807626787',
            'name': '转诊医生',
            'sourceFrom': '0eba233945864c23b25016b2d2126dd1',
            'sourceFromName': '莫沙',
            'relatedType': 1,
            'relatedId': null,
        },
    },
    'registrationFormItem': null,
    'examinationSheetReport': {
        'id': 'ffffffff0000000034bc679944374000',
        'goodsType': 3,
        'type': 2,
        'subType': 10,
        'deviceType': 1,
        'imageFiles': [],
        'method': null,
        'videoDescription': '双基底节区可见多发斑点状低密度灶，边界欠清，病灶大小不一；侧脑室旁对称性脑白质密度减低；侧脑室略增宽，脑沟、脑裂增宽、加深；中线结构居中。',
        'resultInfo': null,
        'advice': '',
        'suggestion': null,
        'diagnosisFlag': 0,
        'versionFlag': 0,
        'recordDoctorId': '6e45706922a74966ab51e4ed1e604641',
        'recordDoctor': {
            'id': '6e45706922a74966ab51e4ed1e604641',
            'name': '宁铁桥',
            'mobile': '***********',
            'countryCode': '86',
            'status': 1,
            'shortId': '939523477594734595',
            'namePy': 'yumeng',
            'namePyFirst': 'YM',
            'headImgUrl': 'https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/clinic-usage/ffffffff000000001ea0b8f80078c000/doctor/2_D4FOMhXaVkeq.png',
            'handSign': 'https://cis-images-dev.oss-cn-shanghai.aliyuncs.com/prescription-sign/pjBynQ8Gypg2rMNtijUcfYvLsCgBoFqt_1704969123981',
            'wechatOpenIdMp': '1',
            'wechatNickName': '刘喜',
        },
        'consultationDoctorId': 'ffffffff000000003499ab72dff4c000',
        'consultationDoctor': {
            'id': 'ffffffff000000003499ab72dff4c000',
            'name': '宁铁桥',
            'mobile': '18113099837',
            'countryCode': null,
            'status': 1,
            'shortId': '3790249071280963584',
            'namePy': 'zhangjinsong|zhangjingsong',
            'namePyFirst': 'ZJS',
            'headImgUrl': 'null',
            'handSign': 'https://cis-images-dev.oss-cn-shanghai.aliyuncs.com/prescription-sign/pjBynQ8Gypg2rMNtijUcfYvLsCgBoFqt_1704969123981',
            'wechatOpenIdMp': 'o_yu51A7CGxh3WO0_pAIiOOi_nOM',
            'wechatNickName': '',
        },
        'principalDoctorId': null,
        'diagnosisEntryItems': [
            {
                'id': '3800026112893337600',
                'type': 2,
                'deviceType': 1,
                'name': '多发腔隙性脑梗死。',
                'abnormalFlag': 0,
            },
            {
                'id': '3800026112893337601',
                'type': 2,
                'deviceType': 1,
                'name': '脑白质脱髓鞘改变。',
                'abnormalFlag': 0,
                'sort': 1,
            },
            {
                'id': '3800026112893337602',
                'type': 2,
                'deviceType': 1,
                'name': '脑萎缩。',
                'abnormalFlag': 0,
                'sort': 2,
            },
        ],
        'recordDoctorName': '于蒙',
        'consultationDoctorName': '张劲松',
        'principalDoctorName': null,
        'principalDoctorMobile': null,
    },
    'samplePipe': null,
    'deviceModelId': '3779579454557454336',
    'deviceId': '3788987003351744512',
    'deviceRoomId': '3785417826818605056',
    'deviceView': {
        'id': '3779579454557454336',
        'deviceModelId': '3779579454557454336',
        'name': '检查仪器-111',
        'model': 'mask-111',
        'deviceUuid': 'mask-1-10',
        'manufacture': '三诺',
        'iconUrl': 'https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/oa/lis-device/image/6yljbPIQwY9SuLnrZOO1rCano4VqExEh_1675855625086.jpg',
        'goodsType': 3,
        'goodsSubType': 2,
        'goodsExtendSpec': '10',
        'deviceType': 1,
        'deviceTypeName': 'CT',
        'usageType': 0,
        'usageTypeName': '未知分类',
        'innerFlag': 0,
        'deviceExtensions': {
            'aeTitle': 'CT001',
        },
        'sampleNoRule': null,
        'connectStatus': 0,
        'deviceModelStatus': 0,
        'deviceId': '3788987003351744512',
        'deviceShortId': '000041',
        'deviceStatus': 10,
        'deviceStatusName': '使用中',
        'deviceRoomId': '3785417826818605056',
        'deviceRoomName': 'CT机房1',
        'department': {
            'id': 'ffffffff0000000034692fc6d5df0000',
            'name': '麻醉科',
            'departmentAddress': null,
            'tagId': 'ffffffff0000000034692fc6d5df0001',
            'type': 1,
            'isDefault': 0,
            'mobile': '',
            'beds': null,
            'customId': 'MZK0008',
            'isClinical': 1,
            'mainMedical': 'f9eb8dbc-a44a-11e9-99a1-acde48001122',
            'mainMedicalName': '麻醉科',
            'mainMedicalCode': '26',
        },
        'extendInfos': {},
    },
    'examinationApplySheetView': {
        'id': '3800026093565984769',
        'chainId': 'ffffffff00000000146808c695534000',
        'clinicId': 'ffffffff00000000146808c695534004',
        'doctorId': '',
        'departmentId': '',
        'patientId': 'ffffffff00000000348b9ddb20138000',
        'patientOrderId': 'ffffffff0000000034bc67994216c000',
        'registrationFormItemId': 'ffffffff0000000034bc679962230002',
        'deviceId': '3788987003351744512',
        'deviceRoomId': '3785417826818605056',
        'no': 'CT2404180001',
        'businessType': 0,
        'goodsType': 3,
        'type': 2,
        'subType': 10,
        'deviceType': 1,
        'chiefComplaint': null,
        'presentHistory': null,
        'physicalExamination': null,
        'diagnosisInfos': [],
        'purpose': null,
        'planExecuteDate': '2024-04-18',
        'created': '2024-04-18T02:56:11Z',
        'status': 20,
        'patient': null,
        'dcm4cheeView': {
            'patient': {
                'SpecificCharacterSet': 'ISO_IR 192',
                'PatientBirthDate': '19780627',
                'PatientSex': 'M',
                'PatientID': '001006',
                'PatientName': '张雪峰',
            },
            'worklist': [
                {
                    'SpecificCharacterSet': 'ISO_IR 192',
                    'ScheduledProcedureStepSequence': [
                        {
                            'ScheduledProcedureStepStartDate': '20240418',
                            'ScheduledStationAETitle': 'CT001',
                            'ScheduledProcedureStepStartTime': '1057',
                            'ScheduledProcedureStepStatus': 'SCHEDULED',
                            'Modality': 'CT',
                            'ScheduledStationName': '',
                        },
                    ],
                    'AccessionNumber': 'CT2404180001',
                    'RequestedProcedureDescription': 'CT检查-1',
                },
            ],
        },
        'doctorName': null,
    },
    'location': {
        'name': 'CT影像科室1',
        'address': '3楼左转200米',
    },
    'peSheetSimpleView': null,
    'deviceType': 1,
    'isMerge': 0,
    'importFlag': 0,
    'updateItemsValueFlag': 1,
    'goodsSubType': 2,
    'innerFlag': 0,
    'departmentId': null,
    'deviceStatus': 10,
    'doctorName': null,
    'sellerName': null,
    'clinicPrintName': 'ABC医院',
    'examinationApplySheetNo': 'CT2404180001',
    'deviceName': '检查仪器-111',
    'lastModifiedMillsTime': '1713409008000',
    'departmentName': null,
    'lastModifiedTime': '2024-04-18T02:56:48Z',
    'name': 'CT检查-1',
    'modifier': {
        'id': '6e45706922a74966ab51e4ed1e604641',
        'name': '于蒙',
        'mobile': '***********',
        'countryCode': '86',
        'status': 1,
        'shortId': '939523477594734595',
        'namePy': 'yumeng',
        'namePyFirst': 'YM',
        'headImgUrl': 'https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/clinic-usage/ffffffff000000001ea0b8f80078c000/doctor/2_D4FOMhXaVkeq.png',
        'handSign': 'https://cis-images-dev.oss-cn-shanghai.aliyuncs.com/prescription-sign/pjBynQ8Gypg2rMNtijUcfYvLsCgBoFqt_1704969123981',
        'wechatOpenIdMp': '1',
        'wechatNickName': '刘喜',
    },
    'extendDiagnosisInfos': null,
    'modifierName': '于蒙',
};
