.outpatient-diagnosis-treatment {
    margin: 10px 0;

    .add-related-product-btn-wrapper {
        // position: relative;
        margin-right: 15px;
        margin-left: 5px;
        cursor: pointer;

        .related-product-title {
            color: $theme1;

            &.no-products {
                color: $T2;

                &:hover {
                    color: $theme1;
                }
            }
        }
    }

    .diagnosis-treatment-recommend-popover-wrapper {
        .abc-popover__reference {
            display: flex;
            align-items: center;
        }

        .add-btn {
            &.is-selected {
                background-color: #eff3f6;
            }
        }
    }

    .abc-form-item {
        margin: 0;
    }

    .abc-input-wrapper.count-center,
    .input-select,
    .no-border-input {
        .abc-input__inner {
            height: 39px;
            padding: 3px 4px;
            background-color: #ffffff;
            border-color: transparent;
            border-radius: 0;
        }

        .append-input {
            min-width: 40px;
            border-color: $P6;
            border-radius: 0;
        }

        &.is-disabled {
            .abc-input__inner {
                background-color: #f9fafc !important;
            }
        }
    }

    .input-select {
        .abc-input__inner {
            text-align: center;
            border-left: 1px solid rgba(0, 0, 0, 0);
        }

        .cis-icon-dropdown_triangle {
            display: none;
        }
    }

    .search-wrapper {
        display: flex;
        align-items: center;
        justify-content: space-between;

        > div {
            position: relative;
            width: 32%;
            background-color: #fffdec;
        }

        .cis-icon-plus {
            font-size: 12px;

            /* color: $T3; */
            color: #b9bccb;
        }
    }

    .goods-item-list {
        background-color: var(--abc-color-S2);
        border: 1px solid var(--abc-color-card-border-color);
        border-top: 0;
        border-radius: 0 0 var(--abc-border-radius-small) var(--abc-border-radius-small);

        li {
            position: relative;
            display: flex;
            align-items: center;
            height: 40px;
            padding: 0;
            border-bottom: 1px dashed $P6;

            &:hover {
                .delete-icon {
                    visibility: inherit;
                }
            }

            &:last-child {
                border-bottom: 0;
            }

            &:not(.search-autocomplete) {
                input::-webkit-input-placeholder {
                    font-size: 12px;
                    line-height: 40px;
                }

                input:-ms-input-placeholder {
                    font-size: 12px;
                    line-height: 40px;
                }

                input::placeholder {
                    font-size: 12px;
                    line-height: 40px;
                }
            }
        }

        .search-autocomplete {
            .goods-autocomplete-wrapper {
                flex: 1;

                .abc-input__inner {
                    border-radius: 0 0 0 var(--abc-border-radius-small);
                }
            }

            .tooth-selector-wrapper + div {
                .goods-autocomplete-wrapper .abc-input__inner {
                    border-radius: 0;
                }
            }

            .total-price {
                display: flex;
                align-items: center;
                justify-content: flex-end;
                min-width: 126px;
                padding-right: 12px;
                margin-left: auto;
                font-weight: 400;
                color: $T2;
                text-align: right;

                .charge-status-img {
                    margin-right: 12px;
                }
            }

            .abc-input__inner {
                height: 40px;
                border-color: transparent;
                border-radius: 0;
            }
        }

        .index {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 30px;
            height: 100%;
            border-right: 1px dashed $P6;
        }

        .tooth-selector-wrapper {
            border-right: 1px dashed $P6;
        }

        .goods-type {
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 41px;
            height: 100%;
            padding: 0 6px;
            color: $T2;
            border-left: 1px dashed $P6;
        }

        .medical-fee-grade-td-wrapper {
            width: 60px;

            & + .name {
                .name-describe {
                    padding-left: 3px;
                }
            }
        }

        .name {
            display: flex;
            flex: 1;
            align-items: center;
            width: 0;
            height: 100%;
            //padding-right: 4px;

            .goods-autocomplete-wrapper {
                width: 100%;
            }

            .abc-input__inner {
                height: 40px;
                border-color: transparent;
                border-radius: 0;
            }

            .name-describe {
                display: flex;
                align-items: center;
                width: 100%;
                height: 100%;
                padding-left: 8px;
                cursor: pointer;

                > div {
                    display: inline-flex;
                    width: 100%;
                    max-width: 100%;
                }
            }

            .ellipsis {
                font-weight: 500;
                cursor: pointer;
            }

            .cis-icon-Attention {
                &.cis-icon-Attention_disabled {
                    line-height: 22px;
                }
            }

            .abc-tipsy {
                color: $Y2;
            }

            img {
                width: 30px;
                margin-left: 4px;
            }

            .abc-tag-wrapper {
                margin-left: 4px;
            }
        }

        .material-goods {
            display: flex;
            align-items: center;
        }

        .no-stock {
            color: $Y2 !important;

            .spec {
                color: $Y2 !important;
            }
        }

        .name-text {
            flex: 0 1 auto;
        }

        .spec {
            display: inline-flex;
            flex: 1 1 30%;
            align-items: center;
            margin-left: 4px;
            font-size: 12px;
            color: $T2;
        }

        .days {
            width: 59px;
            height: 100%;
            border-left: 1px dashed $P6;

            .is-readonly {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 58px;
                height: 40px;
                padding-right: 24px;
                font-size: 14px;
            }
        }

        .freq {
            width: 68px;
            height: 100%;
            border-left: 1px dashed var(--abc-color-P6);
        }

        .unit-count,
        .dosage {
            display: flex;
            align-items: center;
            width: 98px;
            height: 100%;
            border-left: 1px dashed $P6;

            span.unit {
                display: inline-block;
                width: 48px;
                overflow: hidden;
                text-align: center;
                white-space: nowrap;
            }

            .small-font {
                font-size: 12px;
            }
        }

        .unit-price {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            padding-right: 10px;
            color: $T2;
        }

        .ratio {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            width: 7.88%;
            min-width: 58px;
            height: 100%;
            border-left: 1px dashed $P6;
        }

        .total {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            width: 84px;
            min-width: 84px;
            max-width: 84px;
            height: 100%;
            border-left: 1px dashed $P6;

            >span {
                padding-right: 10px;
            }
        }

        .doctor-info,
        .nurse-info {
            width: 67px;
            height: 100%;
            border-left: 1px dashed $P6;

            .is-readonly {
                display: flex;
                align-items: center;
                width: 100%;
                height: 100%;
                padding: 3px 5px;
            }
        }

        .item-remark {
            position: relative;
            width: 59px;
            min-width: 59px;
            max-width: 59px;
            border-left: 1px dashed $P6;

            .iconfont {
                font-size: 14px;
                color: $P1;
            }

            .abc-input__inner {
                font-size: 12px;
                background-color: transparent;
            }

            .append-input {
                position: absolute;
                top: 0;
                right: 0;
                z-index: 2;
                width: 28px;
                min-width: 28px;
                height: 100%;
                padding: 0;
                cursor: pointer;
                background-color: transparent;
                border-color: transparent;

                .input-meridian-wrapper {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 100%;
                    height: 100%;
                }

                .icon-meridian-wrapper {
                    z-index: 2;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 24px;
                    min-width: 24px;
                    height: 24px;
                    background-color: transparent;
                    border-color: transparent;
                    border-radius: 16px;
                }

                &:hover,
                &:active {
                    .icon-meridian-wrapper {
                        background-color: $P4;
                    }

                    .iconfont {
                        color: #96a4b3;
                    }
                }
            }

            .readonly-remark {
                display: flex;
                align-items: center;
                height: 40px;
                padding-left: 4px;
                font-size: 12px;
            }
        }

        .input-append-unit {
            position: absolute;
            top: 0;
            right: 0;
            z-index: 2;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            width: 24px;
            height: 40px;
        }

        .delete-item {
            position: absolute;
            top: 0;
            right: -21px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 20px;
            height: 40px;
            padding: 0;
            line-height: 40px;
            text-align: center;
            cursor: pointer;
            background-color: transparent;

            .delete-icon {
                visibility: hidden;
            }
        }
    }

    .suggestions-wrapper {
        width: 480px;

        .suggestions-item {
            padding: 0 24px 0 12px;
        }
    }

    .popfloat {
        position: absolute;
        top: 36px;
        left: 0;
        z-index: 9999;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100px;
        color: #000000;
        background-color: #ffffff;
        border: 1px solid $P1;
        border-radius: var(--abc-border-radius-small);
        box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.15);

        .content {
            height: 20px;
            line-height: 1;
            color: $T3;
            text-align: center;
        }

        .link {
            color: $theme1;
            cursor: pointer;
        }
    }

    &.is-disabled .goods-item-list,
    .goods-item.is-disabled {
        background-color: var(--abc-color-bg-disabled);

        .ellipsis {
            cursor: not-allowed;
        }

        .abc-form-item-content .abc-input-wrapper .abc-input__inner {
            border: none;
        }

        .delete-item {
            cursor: default;
        }
    }

    .goods-item.is-disabled {
        border-color: $P6;
    }

    &.has-item-remark {
        .item-remark {
            width: 108px;
            min-width: 108px;
            max-width: 108px;
        }
    }
}
