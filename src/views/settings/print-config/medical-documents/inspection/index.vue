<template>
    <biz-setting-layout>
        <biz-setting-content>
            <abc-form ref="printForm" item-no-margin>
                <biz-setting-form :label-width="84" divider-full-screen>
                    <biz-setting-form-header>
                        <medical-technology-report-tab v-if="from === 2">
                        </medical-technology-report-tab>

                        <report-tab v-else></report-tab>
                    </biz-setting-form-header>

                    <biz-setting-form-group title="检验报告前记">
                        <biz-setting-form-item label="抬头名称" label-line-height-size="medium">
                            <abc-flex :gap="8">
                                <abc-form-item required :validate-event="validateName">
                                    <title-setting v-model="postData.header.title" :max-length="titleMaxLength * 2"></title-setting>
                                </abc-form-item>

                                <abc-space>
                                    <abc-button
                                        v-if="!isShowSubtitle"
                                        size="small"
                                        variant="text"
                                        @click="isShowSubtitle = true"
                                    >
                                        加一行
                                    </abc-button>

                                    <abc-button size="small" variant="text" @click="handleSetAllDocuments">
                                        应用至全部
                                    </abc-button>
                                </abc-space>
                            </abc-flex>

                            <abc-flex v-if="isShowSubtitle" :gap="8">
                                <abc-form-item>
                                    <title-setting v-model="postData.header.subtitle" :max-length="titleMaxLength * 2"></title-setting>
                                </abc-form-item>

                                <abc-button
                                    variant="text"
                                    theme="danger"
                                    size="small"
                                    @click="deleteSubTitle"
                                >
                                    删除
                                </abc-button>
                            </abc-flex>
                        </biz-setting-form-item>

                        <biz-setting-form-item label="抬头信息">
                            <abc-flex :gap="12" wrap="wrap">
                                <abc-checkbox v-model="postData.header.logo" type="number" class="checkbox-no-margin">
                                    Logo
                                </abc-checkbox>
                                <abc-checkbox v-model="postData.header.barcode" type="number" class="checkbox-no-margin">
                                    检验单号条码
                                </abc-checkbox>
                                <abc-checkbox v-model="postData.header.examineName" type="number" class="checkbox-no-margin">
                                    项目名称
                                </abc-checkbox>
                                <abc-checkbox v-model="postData.header.patientSn" type="number" class="checkbox-no-margin">
                                    档案号
                                </abc-checkbox>
                            </abc-flex>
                        </biz-setting-form-item>
                    </biz-setting-form-group>

                    <biz-setting-form-group title="检验报告中记">
                        <biz-setting-form-item label="直方图">
                            <abc-flex :gap="12" wrap="wrap">
                                <abc-checkbox v-model="postData.content.bloodHistogram" type="number" class="checkbox-no-margin">
                                    血常规直方图
                                </abc-checkbox>

                                <abc-checkbox v-model="postData.content.itemCode" type="number" class="checkbox-no-margin">
                                    项目代码
                                </abc-checkbox>

                                <abc-checkbox v-model="postData.content.comment" type="number" class="checkbox-no-margin">
                                    备注
                                </abc-checkbox>
                            </abc-flex>
                        </biz-setting-form-item>
                    </biz-setting-form-group>

                    <biz-setting-form-group title="检验报告后记">
                        <biz-setting-form-item label="报告时间">
                            <abc-flex :gap="12" wrap="wrap">
                                <abc-checkbox v-model="postData.footer.applyDate" type="number" class="checkbox-no-margin">
                                    申请时间
                                </abc-checkbox>
                                <abc-checkbox v-model="postData.footer.checkDate" type="number" class="checkbox-no-margin">
                                    审核时间
                                </abc-checkbox>
                                <abc-checkbox v-model="postData.footer.printDate" type="number" class="checkbox-no-margin">
                                    打印时间
                                </abc-checkbox>
                                <abc-checkbox v-model="postData.footer.examineDate" type="number" class="checkbox-no-margin">
                                    检验时间
                                </abc-checkbox>
                            </abc-flex>
                        </biz-setting-form-item>

                        <biz-setting-form-item label="检验者签名">
                            <abc-radio-group v-model="postData.footer.testerSignature">
                                <abc-flex gap="12" wrap="wrap">
                                    <abc-radio :label="0" class="checkbox-no-margin">
                                        电脑签名
                                    </abc-radio>
                                    <abc-radio :label="1" class="checkbox-no-margin">
                                        手写电子签
                                    </abc-radio>
                                    <abc-radio :label="2" class="checkbox-no-margin">
                                        不签名
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>

                        <biz-setting-form-item label="审核者签名">
                            <abc-radio-group v-model="postData.footer.checkerSignature">
                                <abc-flex gap="12" wrap="wrap">
                                    <abc-radio :label="0" class="checkbox-no-margin">
                                        电脑签名
                                    </abc-radio>
                                    <abc-radio :label="1" class="checkbox-no-margin">
                                        手写电子签
                                    </abc-radio>
                                    <abc-radio :label="2" class="checkbox-no-margin">
                                        不签名
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>

                        <biz-setting-form-item label="页尾备注" label-line-height-size="medium">
                            <abc-textarea
                                v-model="postData.footer.remark"
                                :height="70"
                                :width="486"
                                placeholder="输入备注内容"
                                maxlength="96"
                            >
                            </abc-textarea>
                        </biz-setting-form-item>

                        <biz-setting-form-item v-if="canRenderIdentificationField" label="互认描述">
                            <abc-checkbox v-model="postData.footer.mutualRecognitionDesc" type="number" class="checkbox-no-margin is-blue">
                                <span>注：带</span>
                                <abc-icon icon="s-baotouinspection-line" size="16"></abc-icon>
                                <span>标识项目为包头市互认项目！</span>
                                <span>带</span>
                                <abc-icon icon="s-headcalltest-fill" size="16"></abc-icon>
                                <span>为呼包互认HR项目！</span>
                            </abc-checkbox>
                        </biz-setting-form-item>
                    </biz-setting-form-group>
                </biz-setting-form>
            </abc-form>

            <template #footer>
                <biz-setting-footer>
                    <abc-space>
                        <abc-button :disabled="disabled" :loading="btnLoading" @click="handleSave">
                            保存
                        </abc-button>
                        <abc-button type="blank" @click="handleReset">
                            恢复默认
                        </abc-button>
                    </abc-space>
                </biz-setting-footer>
            </template>
        </biz-setting-content>

        <biz-setting-sidebar>
            <preview-layout>
                <template slot="previewTab">
                    <div class="preview-tab-item preview-tab-item__active">
                        检验报告
                    </div>
                </template>
                <div slot="previewHtml" ref="previewMountPoint"></div>
            </preview-layout>
        </biz-setting-sidebar>
    </biz-setting-layout>
</template>

<script>
    import PreviewLayout from '../../components/preview-layout.vue';
    import TitleSetting from 'views/settings/print-config/components/title-setting';
    import clone from 'utils/clone.js';
    import { isEqual } from 'utils/lodash.js';

    import { validateName } from 'views/inventory/goods/utils.js';
    import { mapGetters } from 'vuex';
    import PrintAPI from 'api/print.js';

    import MedicalDocumentsMixins from '../mixins/index.js';
    import MixinPrint from 'views/settings/print-config/medical-documents/mixin-print';
    import store from '@/store';
    import { EXAMPLE_DATA } from './constants';
    import { getLengthWithFullCharacter } from 'views/settings/print-config/utils';
    import { isBaoTouOrHuHeHaoTeCity } from '@/utils';
    import {
        BizSettingForm,
        BizSettingFormGroup,
        BizSettingFormItem,
        BizSettingFormHeader,
    } from '@/components-composite/setting-form/index.js';

    import {
        BizSettingLayout,
        BizSettingSidebar,
        BizSettingContent,
        BizSettingFooter,
    } from '@/components-composite/setting-form-layout/index.js';
    import ReportTab from 'views/settings/print-config/components/report-tab/index.vue';
    import MedicalTechnologyReportTab from '@/views-hospital/settings-common/frames/medical-technology-print-config/components/report-tab/report-tab.vue';

    const ResetData = {
        'header': { // 处方前记
            'title': '', // 抬头名称 字符串必填
            'subtitle': '', // 副抬头名称 可选值
            'logo': 1, // Logo
            'examineName': 1, // 项目名称
            'barcode': 0, // 检验单号条码
            'patientSn': 0,//档案号
        },
        content: {
            bloodHistogram: 1, // 血常规直方图
            'itemCode': 1, //项目代码
            'comment': 0,//备注
        },
        'footer': {
            'applyDate': 1, // 申请时间
            'checkDate': 1, // 审核时间
            'printDate': 1, // 打印时间
            'remark': '', // 页尾备注
            'examineDate': 1, //检验时间
            'testerSignature': 0, // 检验者签名
            'checkerSignature': 0, // 审核者签名
            'mutualRecognitionDesc': 0, // 互认描述
        },
    };
    export default {
        name: 'InspectionPrintConfig',
        components: {
            ReportTab,
            BizSettingFormHeader,
            PreviewLayout,
            TitleSetting,
            BizSettingForm,
            BizSettingFormGroup,
            BizSettingFormItem,
            BizSettingLayout,
            BizSettingSidebar,
            BizSettingContent,
            BizSettingFooter,
            MedicalTechnologyReportTab,
        },
        mixins: [MedicalDocumentsMixins, MixinPrint],
        props: {
            from: {
                type: Number,
                default: 1,
            },
        },
        data() {
            return {
                postData: clone(ResetData),
                btnLoading: false,
                templateStr: '',
                isShowSubtitle: false,
                titleMaxLength: 15, // 标题最大长度
                postDataCache: null,
            };
        },
        computed: {
            ...mapGetters([
                'printMedicalDocumentsConfig',
                'currentClinic',
            ]),
            ...mapGetters('viewDistribute',[
                'viewDistributeConfig',
            ]),
            isSupportIdentification() {
                return this.viewDistributeConfig.Settings.print.examinationReport.isSupportIdentification;
            },
            disabled() {
                if (!this.postDataCache) return true;
                return isEqual(this.postData, this.postDataCache);
            },
            currentExampleData() {
                return EXAMPLE_DATA;
            },

            // 互认描述：包头市 + 医院
            canRenderIdentificationField() {
                return isBaoTouOrHuHeHaoTeCity(this.clinicBasicConfig.addressCityId) &&
                    this.isSupportIdentification;
            },
        },

        watch: {
            'printMedicalDocumentsConfig.examineReport': {
                handler() {
                    if (this.printMedicalDocumentsConfig.examineReport) {
                        const cachePostData = clone(this.printMedicalDocumentsConfig.examineReport);
                        // 判断抬头是否超过12个字符(需分区全角/半角),将超过部分拆分到副抬头
                        const {
                            fullCharacterLength, splitLength,
                        } = getLengthWithFullCharacter(cachePostData.header.title, this.titleMaxLength);
                        if (fullCharacterLength > this.titleMaxLength) {
                            const cacheTitle = cachePostData.header.title;
                            cachePostData.header.title = cacheTitle.slice(0, splitLength);
                            cachePostData.header.subtitle = cacheTitle.slice(splitLength);
                            this.postData = cachePostData;
                            this.updateTitleWithParams('examineReport');
                        } else {
                            this.postData = cachePostData;
                        }

                        if (!this.postData.header.title) {
                            const cacheClinicName = this.currentClinic.clinicName;
                            const {
                                fullCharacterLength: fullClinicCharacterLength, splitLength: splitClinicLength,
                            } = getLengthWithFullCharacter(cacheClinicName, this.titleMaxLength);
                            if (fullClinicCharacterLength > this.titleMaxLength) {
                                this.postData.header.title = cacheClinicName.slice(0, splitClinicLength);
                                this.postData.header.subtitle = cacheClinicName.slice(splitClinicLength);
                            } else {
                                this.postData.header.title = cacheClinicName;
                            }
                        }
                        this.isShowSubtitle = !!this.postData.header.subtitle;

                        this.postDataCache = clone(this.postData);
                    }
                },
                immediate: true,
                deep: true,
            },
        },
        methods: {
            validateName,
            instanceGlobalConfigHandler(newValue) {
                const newInstanceGlobalConfig = clone(store.getters.printGlobalConfig);
                newInstanceGlobalConfig.medicalDocuments.examineReport = newValue;
                return newInstanceGlobalConfig;
            },
            getCurrentTemplate() {
                return window.AbcPackages.AbcTemplates.examinationReport;
            },
            handleSave() {
                this.$refs.printForm.validate(async (val) => {
                    if (val) {
                        await this.updatePrintConfig();
                    }
                });
            },
            async updatePrintConfig() {
                try {
                    this.btnLoading = true;
                    const { data } = await PrintAPI.updatePrintConfig('clinic', 'print.medicalDocuments.examineReport', {
                        examineReport: this.postData,
                    });
                    this.$store.commit('SET_PRINT_MEDICAL_DOCUMENTS_SUB_CONFIG', {
                        key: 'examineReport',
                        data: data.examineReport,
                    });
                    this.btnLoading = false;
                    this.$Toast({
                        message: '保存成功',
                        type: 'success',
                    });
                } catch (e) {
                    this.btnLoading = false;
                }
            },
            handleReset() {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '是否确认将当前全部设置恢复为系统默认设置？',
                    onConfirm: () => {
                        this.postData = clone(ResetData);

                        // 如果门店名称超过15(全角)/30(半角)个字,则进行拆分
                        const cacheClinicName = this.currentClinic.clinicName;
                        const {
                            fullCharacterLength: fullClinicCharacterLength, splitLength: splitClinicLength,
                        } = getLengthWithFullCharacter(cacheClinicName, this.titleMaxLength);
                        if (fullClinicCharacterLength > this.titleMaxLength) {
                            this.postData.header.title = cacheClinicName.slice(0, splitClinicLength);
                            this.postData.header.subtitle = cacheClinicName.slice(splitClinicLength);
                        } else {
                            this.postData.header.title = cacheClinicName;
                        }
                    },
                });

            },
            handleSetAllDocuments() {
                this.$refs.printForm.validate((val) => {
                    if (val) {
                        let content = `是否确认将全部医疗文书抬头名称统一修改为“${this.postData.header.title}”？`;
                        if (this.postData.header.subtitle) {
                            content = `是否确认将全部医疗文书抬头名称统一修改为“${this.postData.header.title}”和“${this.postData.header.subtitle}”？`;
                        }
                        // 设置为全部医疗文书
                        this.$confirm({
                            type: 'warn',
                            title: '提示',
                            content,
                            onConfirm: async () => {
                                await this.updateTitle();
                                this.$Toast({
                                    message: '设置成功',
                                    type: 'success',
                                });
                            },
                        });
                    }
                });
            },
            /**
             * 删除副抬头
             */
            deleteSubTitle() {
                this.isShowSubtitle = false;
                this.postData.header.subtitle = '';
            },
        },
    };
</script>

