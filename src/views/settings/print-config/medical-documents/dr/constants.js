export const EXAMPLE_DATA = {
    'id': 'ffffffff0000000034bc67f844374000',
    'goodsType': 3,
    'type': 2,
    'subType': 10,
    'patientId': 'ffffffff00000000348b9ddb20138000',
    'patientOrderId': 'ffffffff0000000034bc67f84216c000',
    'examinationApplySheetId': '3800026501587877889',
    'wardAreaId': null,
    'relationPatientOrderId': null,
    'chargeSheetId': null,
    'chargeFormItemId': null,
    'chargeSheetType': -1,
    'outpatientFormItemId': null,
    'adviceExecuteItemId': null,
    'peFormItemId': null,
    'peFormItemIds': null,
    'organPrintView': {
        'id': 'ffffffff00000000146808c695534004',
        'name': 'ABC医院',
        'contactPhone': '40008009',
        'addressProvinceName': '内蒙古',
        'addressCityName': '呼和浩特市',
        'addressDistrictName': '新城区',
        'addressDetail': '',
        'logo': 'https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/clinic-usage/ffffffff00000000146808c695534004/basic/logo_h_square_%E5%8F%A3%E8%85%94%E7%AE%A1%E5%AE%B6_6pctmlBmtqmq_ElI982vHz7Nn.jpg',
        'qrUrl': 'http://weixin.qq.com/q/02SxZ5F5JyeUD10000M07k',
        'category': '综合医院',
        'addressProvinceId': '150000',
        'addressCityId': '150100',
        'addressDistrictId': '150102',
        'hisType': 100,
        'medicalDocumentsTitle': {
            'prescription': 'ABC医院',
            'medical': 'ABC医院',
            'infusion': 'ABC医院',
            'treatment': 'ABC医院',
            'examination': 'ABC医院',
            'illnessCert': 'ABC医院',
            'inspection': null,
        },
    },
    'barCode': 19181,
    'businessType': 50,
    'orderNo': '202404180007',
    'sampleNo': '202404180007',
    'status': 1,
    'sampleStatus': 10,
    'doctorId': '',
    'sellerId': null,
    'doctorDepartmentId': null,
    'doctorDepartmentName': null,
    'sellerDepartmentId': null,
    'sellerDepartmentName': null,
    'executeDepartmentId': '',
    'executeDepartmentName': null,
    'attachments': [],
    'remark': null,
    'itemsValue': null,
    'deviceData': null,
    'preItemsValue': null,
    'examinationHistories': null,
    'created': '2024-04-18T03:08:51Z',
    'orderByDate': '2024-04-18T03:08:51Z',
    'lastModifiedBy': '6e45706922a74966ab51e4ed1e604641',
    'patientOrderNumber': '202404180007',
    'wardAreaName': null,
    'bedNumber': null,
    'testerId': '6e45706922a74966ab51e4ed1e604641',
    'testerName': '于蒙',
    'tester': {
        'id': '6e45706922a74966ab51e4ed1e604641',
        'name': '于蒙',
        'mobile': '***********',
        'countryCode': '86',
        'status': 1,
        'shortId': '939523477594734595',
        'namePy': 'yumeng',
        'namePyFirst': 'YM',
        'headImgUrl': 'https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/clinic-usage/ffffffff000000001ea0b8f80078c000/doctor/2_D4FOMhXaVkeq.png',
        'handSign': 'https://cis-images-dev.oss-cn-shanghai.aliyuncs.com/prescription-sign/pjBynQ8Gypg2rMNtijUcfYvLsCgBoFqt_1704969123981',
        'wechatOpenIdMp': '1',
        'wechatNickName': '刘喜',
    },
    'testTime': '2024-04-18T03:09:00Z',
    'sampleType': null,
    'checkerId': '6e45706922a74966ab51e4ed1e604641',
    'checkerName': '于蒙',
    'checker': {
        'id': '6e45706922a74966ab51e4ed1e604641',
        'name': '于蒙',
        'mobile': '***********',
        'countryCode': '86',
        'status': 1,
        'shortId': '939523477594734595',
        'namePy': 'yumeng',
        'namePyFirst': 'YM',
        'headImgUrl': 'https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/clinic-usage/ffffffff000000001ea0b8f80078c000/doctor/2_D4FOMhXaVkeq.png',
        'handSign': 'https://cis-images-dev.oss-cn-shanghai.aliyuncs.com/prescription-sign/pjBynQ8Gypg2rMNtijUcfYvLsCgBoFqt_1704969123981',
        'wechatOpenIdMp': '1',
        'wechatNickName': '刘喜',
    },
    'checkTime': '2024-04-18T03:09:00Z',
    'reportTime': '2024-04-19T02:07:00Z',
    'chargeFormItemStatus': 0,
    'canExecute': 1,
    'diagnosis': null,
    'patient': {
        'id': 'ffffffff00000000348b9ddb20138000',
        'name': '张雪峰',
        'namePy': 'zhangxuefeng',
        'namePyFirst': 'ZXF',
        'mobile': '',
        'countryCode': '86',
        'sex': '男',
        'birthday': '1978-06-27',
        'age': {
            'year': 45,
            'month': 9,
            'day': 21,
        },
        'isMember': 0,
        'idCard': '',
        'marital': null,
        'weight': null,
        'importFlag': 0,
        'ethnicity': '',
        'nationality': null,
        'contactName': null,
        'contactRelation': null,
        'contactMobile': null,
        'sn': '001006',
        'remark': '',
        'profession': '',
        'company': '',
        'companyMobile': null,
        'blockFlag': 0,
        'address': {
            'addressCityId': null,
            'addressCityName': null,
            'addressDetail': '',
            'addressDistrictId': null,
            'addressDistrictName': null,
            'addressGeo': null,
            'addressProvinceId': null,
            'addressProvinceName': null,
            'addressPostcode': null,
            'fullAddress': '',
        },
        'familyMobile': null,
        'tags': null,
        'activeDate': '2023-09-15T05:33:46.000+00:00',
        'activeClinicId': 'ffffffff00000000146808c695534004',
        'lastOutpatientDate': '2023-09-15T05:33:45.000+00:00',
        'lastOutpatientClinicId': 'ffffffff00000000146808c695534004',
        'wxOpenId': null,
        'unionId': null,
        'wxUserId': null,
        'wxNickName': null,
        'wxHeadImgUrl': null,
        'isWxMainPatient': 0,
        'wxBindStatus': 0,
        'patientSource': {
            'parentId': null,
            'parentName': null,
            'id': '11413126450807626787',
            'name': '转诊医生',
            'sourceFrom': '0eba233945864c23b25016b2d2126dd1',
            'sourceFromName': '莫沙',
            'relatedType': 1,
            'relatedId': null,
        },
    },
    'registrationFormItem': null,
    'examinationSheetReport': {
        'id': 'ffffffff0000000034bc67f844374000',
        'goodsType': 3,
        'type': 2,
        'subType': 10,
        'deviceType': 2,
        'imageFiles': [],
        'method': null,
        'videoDescription': '两侧胸廓对称,气管居中；双侧肺野透过度正常，纹理走行正常；双肺门无增大、增浓；心影大小、形态如常；纵隔居中，两侧膈面光整，肋膈角清晰锐利。',
        'resultInfo': null,
        'advice': '',
        'suggestion': null,
        'diagnosisFlag': 0,
        'versionFlag': 0,
        'recordDoctorId': '6e45706922a74966ab51e4ed1e604641',
        'recordDoctor': {
            'id': '6e45706922a74966ab51e4ed1e604641',
            'name': '于蒙',
            'mobile': '***********',
            'countryCode': '86',
            'status': 1,
            'shortId': '939523477594734595',
            'namePy': 'yumeng',
            'namePyFirst': 'YM',
            'headImgUrl': 'https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/clinic-usage/ffffffff000000001ea0b8f80078c000/doctor/2_D4FOMhXaVkeq.png',
            'handSign': 'https://cis-images-dev.oss-cn-shanghai.aliyuncs.com/prescription-sign/pjBynQ8Gypg2rMNtijUcfYvLsCgBoFqt_1704969123981',
            'wechatOpenIdMp': '1',
            'wechatNickName': '刘喜',
        },
        'consultationDoctorId': '6e45706922a74966ab51e4ed1e604641',
        'consultationDoctor': {
            'id': '6e45706922a74966ab51e4ed1e604641',
            'name': '于蒙',
            'mobile': '***********',
            'countryCode': '86',
            'status': 1,
            'shortId': '939523477594734595',
            'namePy': 'yumeng',
            'namePyFirst': 'YM',
            'headImgUrl': 'https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/clinic-usage/ffffffff000000001ea0b8f80078c000/doctor/2_D4FOMhXaVkeq.png',
            'handSign': 'https://cis-images-dev.oss-cn-shanghai.aliyuncs.com/prescription-sign/pjBynQ8Gypg2rMNtijUcfYvLsCgBoFqt_1704969123981',
            'wechatOpenIdMp': '1',
            'wechatNickName': '刘喜',
        },
        'principalDoctorId': null,
        'diagnosisEntryItems': [
            {
                'id': '3800026520378359808',
                'diagnosisEntryId': '3796159178812719105',
                'type': 2,
                'deviceType': 2,
                'name': '心肺未见明显异常。',
                'abnormalFlag': 0,
            },
        ],
        'recordDoctorName': '于蒙',
        'consultationDoctorName': '于蒙',
        'principalDoctorName': null,
        'principalDoctorMobile': null,
    },
    'samplePipe': null,
    'deviceModelId': '5732122144155646',
    'deviceId': '3783292401189732352',
    'deviceRoomId': '3780189676869894144',
    'deviceView': {
        'id': '5732122144155646',
        'deviceModelId': '5732122144155646',
        'name': '缓存设备',
        'model': '缓存',
        'deviceUuid': 'cache',
        'manufacture': 'mask',
        'goodsType': 3,
        'goodsSubType': 2,
        'goodsExtendSpec': '10',
        'deviceType': 2,
        'deviceTypeName': 'DR',
        'usageType': 0,
        'usageTypeName': '未知分类',
        'innerFlag': 0,
        'deviceExtensions': {
            'aeTitle': 'DR001',
        },
        'sampleNoRule': null,
        'connectStatus': 0,
        'deviceModelStatus': 0,
        'deviceId': '3783292401189732352',
        'deviceShortId': '000021',
        'deviceStatus': 10,
        'deviceStatusName': '使用中',
        'deviceRoomId': '3780189676869894144',
        'deviceRoomName': 'testceee44',
        'department': {
            'id': 'ffffffff0000000034692fb9d5df0000',
            'name': '呼吸内科',
            'departmentAddress': '6楼',
            'tagId': 'ffffffff0000000034692fb9d5df0001',
            'type': 1,
            'isDefault': 0,
            'mobile': '',
            'beds': null,
            'customId': 'NK0004',
            'isClinical': 1,
            'mainMedical': 'f7286de8-a44a-11e9-99a1-acde48001122',
            'mainMedicalName': '内科',
            'mainMedicalCode': '03',
        },
        'extendInfos': {},
    },
    'examinationApplySheetView': {
        'id': '3800026501587877889',
        'chainId': 'ffffffff00000000146808c695534000',
        'clinicId': 'ffffffff00000000146808c695534004',
        'doctorId': '',
        'departmentId': '',
        'patientId': 'ffffffff00000000348b9ddb20138000',
        'patientOrderId': 'ffffffff0000000034bc67f84216c000',
        'registrationFormItemId': 'ffffffff0000000034bc67f862230002',
        'deviceId': '3783292401189732352',
        'deviceRoomId': '3780189676869894144',
        'no': 'DR2404180001',
        'businessType': 0,
        'goodsType': 3,
        'type': 2,
        'subType': 10,
        'deviceType': 2,
        'chiefComplaint': null,
        'presentHistory': null,
        'physicalExamination': null,
        'diagnosisInfos': [],
        'purpose': null,
        'planExecuteDate': '2024-04-18',
        'created': '2024-04-18T03:08:51Z',
        'status': 20,
        'patient': null,
        'dcm4cheeView': {
            'patient': {
                'SpecificCharacterSet': 'ISO_IR 192',
                'PatientBirthDate': '19780627',
                'PatientSex': 'M',
                'PatientID': '001006',
                'PatientName': '张雪峰',
            },
            'worklist': [
                {
                    'SpecificCharacterSet': 'ISO_IR 192',
                    'ScheduledProcedureStepSequence': [
                        {
                            'ScheduledProcedureStepStartDate': '20240418',
                            'ScheduledStationAETitle': 'DR001',
                            'ScheduledProcedureStepStartTime': '1109',
                            'ScheduledProcedureStepStatus': 'SCHEDULED',
                            'Modality': 'DX',
                            'ScheduledStationName': '',
                        },
                    ],
                    'AccessionNumber': 'DR2404180001',
                    'RequestedProcedureDescription': '骨盆DR',
                },
            ],
        },
        'doctorName': null,
    },
    'location': null,
    'peSheetSimpleView': null,
    'deviceType': 2,
    'isMerge': 0,
    'importFlag': 0,
    'updateItemsValueFlag': 1,
    'goodsSubType': 2,
    'innerFlag': 0,
    'departmentId': null,
    'deviceStatus': 10,
    'doctorName': null,
    'sellerName': null,
    'clinicPrintName': 'ABC医院',
    'examinationApplySheetNo': 'DR2404180001',
    'deviceName': '缓存设备',
    'lastModifiedMillsTime': '1713409766000',
    'departmentName': null,
    'lastModifiedTime': '2024-04-18T03:09:26Z',
    'name': '骨盆DR',
    'modifier': {
        'id': '6e45706922a74966ab51e4ed1e604641',
        'name': '于蒙',
        'mobile': '***********',
        'countryCode': '86',
        'status': 1,
        'shortId': '939523477594734595',
        'namePy': 'yumeng',
        'namePyFirst': 'YM',
        'headImgUrl': 'https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/clinic-usage/ffffffff000000001ea0b8f80078c000/doctor/2_D4FOMhXaVkeq.png',
        'handSign': 'https://cis-images-dev.oss-cn-shanghai.aliyuncs.com/prescription-sign/pjBynQ8Gypg2rMNtijUcfYvLsCgBoFqt_1704969123981',
        'wechatOpenIdMp': '1',
        'wechatNickName': '刘喜',
    },
    'extendDiagnosisInfos': null,
    'modifierName': '于蒙',
};
