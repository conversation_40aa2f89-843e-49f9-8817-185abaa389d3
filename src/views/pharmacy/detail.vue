<template>
    <div
        v-abc-loading:page="loading"
        :class="{
            'order-is-dispensing': isDispensing,
            'order-is-close': isClose,
        }"
        data-cy="pharmacy-main-content"
    >
        <abc-container-center-top-head>
            <abc-space size="middle">
                <abc-text size="large" theme="black" bold>
                    发药
                </abc-text>

                <abc-space>
                    <abc-tag-v2 v-if="curPharmacyName" variant="light-outline">
                        {{ curPharmacyName }}
                    </abc-tag-v2>

                    <abc-text v-if="airPharmacyOrderId" size="mini" theme="gray">
                        ABC空中药房
                    </abc-text>
                </abc-space>
            </abc-space>

            <!-- 已发药 -->
            <img
                v-if="isDispensing"
                class="charge-seal"
                src="~assets/images/Dispensing.png"
                alt=""
                data-cy="pharmacy-icon-dispensed"
            />

            <!-- 已退药 -->
            <img
                v-if="isRefundFee"
                class="charge-seal"
                src="~assets/images/dispensing_back.png"
                alt=""
                data-cy="pharmacy-icon-refund-medicine"
            />

            <!-- 已关闭 -->
            <img
                v-if="isClose && !isRefunded"
                class="charge-seal"
                src="~assets/images/icon/<EMAIL>"
                alt=""
                data-cy="pharmacy-icon-closed"
            />

            <!-- 已退费 -->
            <img
                v-if="isRefunded"
                class="charge-seal"
                src="~assets/images/yituifei.png"
                alt=""
                data-cy="pharmacy-icon-refund-fee"
            />

            <abc-flex style="flex: 1;" justify="flex-end">
                <abc-space :size="4">
                    <template v-if="isClose && !isRefunded">
                        <abc-button
                            variant="ghost"
                            :disabled="buttonLoading"
                            data-cy="pharmacy-form-reopen-btn"
                            @click="reopenHandler"
                        >
                            重新打开
                        </abc-button>
                    </template>

                    <template v-else>
                        <abc-check-access v-if="dispensingCallingItemId">
                            <abc-button
                                style=" min-width: 102px;"
                                :loading="callLoading"
                                icon="s-volume-line"
                                variant="ghost"
                                @click="handleCallDispensary"
                            >
                                呼叫取药
                            </abc-button>
                        </abc-check-access>

                        <abc-tooltip :content="tipContent" :disabled="isSelectDrugBeforeDispensing">
                            <div>
                                <abc-check-access v-if="status === dispenseStatusEnum.WAITING">
                                    <abc-button
                                        data-cy="pharmacy-form-dispensing-btn"
                                        :loading="buttonLoading"
                                        :disabled="!isSelectDrugBeforeDispensing"
                                        @click="confirmDelivery"
                                    >
                                        发药
                                    </abc-button>
                                </abc-check-access>
                            </div>
                        </abc-tooltip>

                        <abc-check-access v-if="canUnDispense">
                            <abc-button
                                variant="ghost"
                                data-cy="pharmacy-form-refund-btn"
                                :disabled="buttonLoading || isRefundFee"
                                @click="handleRefund"
                            >
                                退药
                            </abc-button>
                        </abc-check-access>

                        <abc-button
                            v-if="status === dispenseStatusEnum.RETURN && !!supportReDispense"
                            :disabled="buttonLoading"
                            variant="ghost"
                            data-cy="pharmacy-form-redispense-btn"
                            @click="reDispense"
                        >
                            重新发药
                        </abc-button>

                        <abc-tooltip v-if="traceCodeCollectionCheck" content="本单项目均无需采集追溯码" :disabled="traceCodeDispenseItems.length !== 0">
                            <abc-button
                                variant="ghost"
                                :disabled="traceCodeDispenseItems.length === 0"
                                data-cy="trace-code-button"
                                @click="handleOpenTraceCodeDialog"
                            >
                                追溯码
                            </abc-button>
                        </abc-tooltip>

                        <print-popper
                            v-model="printOpt.printSelect"
                            size="small"
                            :width="64"
                            :style="{
                                right: 0,
                                marginLeft: 0
                            }"
                            placement="bottom"
                            :options="printOptions"
                            @print="printHandler"
                            @select-print-setting="openPrintConfigSettingDialog"
                        >
                        </print-popper>

                        <abc-button
                            v-if="status === dispenseStatusEnum.WAITING &&
                                isPartDispensed === 0 &&
                                !isDaijianCenter && !supportReDispense"
                            variant="ghost"
                            data-cy="pharmacy-form-close-btn"
                            :disabled="buttonLoading || isCustomization || disableByLock"
                            @click="closeHandler"
                        >
                            关闭
                        </abc-button>
                    </template>

                    <abc-button
                        variant="ghost"
                        icon="s-b-settings-line"
                        @click="showSettingsDialog = true"
                    >
                    </abc-button>
                </abc-space>
            </abc-flex>
        </abc-container-center-top-head>

        <abc-container-center-main-content>
            <abc-tips-card-v2
                v-if="disableByLock"
                theme="warning"
                style="margin-bottom: 16px;"
                align="center"
                border-radius
            >
                {{ `收费员${lockedInfo.employeeName}正在退费中，退费结束前不可操作` }}
            </abc-tips-card-v2>
            <div class="dispensings-form-wrapper">
                <abc-form ref="dispenseForm">
                    <abc-flex gap="8" align="center">
                        <abc-flex flex="1">
                            <patient-section
                                :key="patient.id"
                                v-model="patient"
                                :disabled="true"
                                :loading="loading"
                                :is-can-see-patient-mobile="isCanSeePatientMobileInPharmacy"
                                :default-patient="selectedPatient"
                                size="medium"
                                @update-patient="changePatientInfo"
                            ></patient-section>
                        </abc-flex>

                        <abc-form-item-group label-position="inner">
                            <abc-form-item
                                v-if="showSellerInput"
                                style="margin: 0;"
                                label="开单人"
                                disabled
                            >
                                <abc-input
                                    disabled
                                    :width="120"
                                    size="large"
                                    :value="departmentSellerStr"
                                ></abc-input>
                            </abc-form-item>
                        </abc-form-item-group>

                        <abc-form-item-group label-position="inner" class="dispense-employee-form-item-group">
                            <abc-form-item style="margin: 0;" label="发药人" :disabled="Boolean(status)">
                                <dispenser-selector
                                    :key="$route.params.id"
                                    v-model="postData.dispensedByIds"
                                    size="medium"
                                    :employees="employeeAll"
                                    :dispensed-by-employee="dispensedByEmployee"
                                    :status="status"
                                    :is-default-dispensed-by-employee-modified="true"
                                    @change="changeDispensedBy"
                                ></dispenser-selector>
                            </abc-form-item>
                        </abc-form-item-group>
                    </abc-flex>

                    <!-- <delivery-info
                        v-if="(deliveryType && deliveryInfo) || postData.isDecoction"
                        :id="$route.params.id"
                        :key="$route.params.id"
                        :is-decoction="postData.isDecoction"
                        :delivery-type="deliveryType"
                        :status="status"
                        :delivery-info="deliveryInfo"
                        :company-id.sync="postData.deliveryCompanyId"
                        :order-no.sync="postData.deliveryOrderNo"
                    ></delivery-info> -->
                    <div
                        :style="{
                            marginBottom: '16px'
                        }"
                    >
                        <!-- 药事服务 -->
                        <service
                            :key="serviceKey"
                            :vendor-name="vendorName"
                            :is-replace-decoct="isReplaceDecoct"
                            :is-virtual-pharmacy="detailProvide.isVirtualPharmacy"
                            :format-process="formatProcess"
                            :format-review="formatOrderReview"
                            :format-compounded="formatOrderCompounded"
                            :other-dispensing-sheets="otherDispensingSheets"
                            :dispensing-form="dispensingForms"
                            :delivery-info="deliveryInfo"
                            :delivered-status="deliveredStatus"
                            :review-config="reviewConfig"
                            :cur-pharmacy-name="curPharmacyName"
                            :cur-pharmacy="curPharmacy"
                            :is-custom="isCustomization"
                            :compounded-config="compoundedConfig"
                            :status="status"
                            :patient="selectedPatient"
                            :employees="employeeActive"
                            :patient-order-id="patientOrderId"
                            @fetchDetail="fetchDetail"
                            @refreshQuickList="refreshQuickList"
                            @changeVendor="changeVendor"
                        ></service>
                    </div>

                    <!-- 发药单表单 -->
                    <form-table
                        :key="$route.params.id"
                        :forms="dispensingForms"
                        :status="status"
                        :is-whole-bill-charge="isWholeBillCharge"
                        :can-part-un-dispense="canPartUnDispense"
                        @change="changeForms"
                        @change-count="handleChangeCount"
                    ></form-table>

                    <advise v-if="doctorAdvice" :doctor-advice="doctorAdvice">
                    </advise>

                    <abc-flex v-if="otherDispensingSheets.length" align="flex-start" style="padding: 10px 12px; margin-top: 16px; background: #ffffff; border: 1px solid var(--abc-color-P7); border-radius: var(--abc-border-radius-small);">
                        <abc-text bold style="margin-right: 24px;">
                            关联发药单
                        </abc-text>

                        <abc-flex vertical gap="8">
                            <abc-flex
                                v-for="(item, index) in otherDispensingSheets"
                                :key="index"
                                align="center"
                            >
                                <abc-text theme="gray">
                                    {{ item.pharmacyName }}（{{ otherDispensingSheets.length }}个发药单）
                                </abc-text>

                                <abc-tag-v2
                                    shape="square"
                                    size="tiny"
                                    theme="default"
                                    variant="light-outline"
                                >
                                    {{ item.status | resolvePharmacyStatusName }}
                                </abc-tag-v2>
                            </abc-flex>
                        </abc-flex>
                    </abc-flex>

                    <abc-flex
                        v-if="airPharmacyOrderId"
                        align="center"
                        style="height: 40px; padding-left: 12px; margin-top: 16px; background: #ffffff; border: 1px solid var(--abc-color-P7); border-radius: var(--abc-border-radius-small);"
                    >
                        <abc-text theme="gray">
                            订单号：{{ airPharmacyOrderId }}
                        </abc-text>
                    </abc-flex>

                    <abc-flex
                        v-if="formatRecord.length > 0"
                        vertical
                        style="margin-top: 16px;"
                        align="flex-start"
                    >
                        <abc-button
                            variant="text"
                            size="small"
                            style="margin-left: 6px;"
                            @click="recordUnfold = !recordUnfold"
                        >
                            操作记录
                            <abc-icon v-if="!recordUnfold" icon="s-b-downline-medium"></abc-icon>
                            <abc-icon v-else icon="s-b-upline-medium"></abc-icon>
                        </abc-button>

                        <abc-flex
                            gap="8"
                            vertical
                            :class="['record-wrapper', {
                                'hide': !recordUnfold
                            }]"
                            style="margin-top: 8px;"
                        >
                            <abc-flex
                                v-for="(o, key) in formatRecord"
                                :key="key"
                                style="padding: 0 12px;"
                                align="flex-start"
                            >
                                <abc-text size="mini" theme="gray" style="width: 120px;">
                                    {{ o.date }}
                                </abc-text>
                                <abc-text size="mini" theme="gray" style="width: 80px;">
                                    {{ o.name }}
                                </abc-text>
                                <abc-text size="mini" theme="gray" style="width: 80px;">
                                    {{ o.type }}
                                </abc-text>

                                <abc-flex flex="1" align="center">
                                    <abc-text size="mini" theme="gray">
                                        {{ o.content }}
                                    </abc-text>

                                    <abc-popover
                                        v-if="o.showDetail"
                                        trigger="hover"
                                        placement="bottom-start"
                                        theme="yellow"
                                        size="large"
                                        popper-class="pharmacy-operation-popover_wrapper"
                                        :visible-arrow="false"
                                    >
                                        <div>
                                            <template v-if="o.isFormDetail">
                                                <abc-content-empty
                                                    v-if="!o.detail || !o.detail.forms || !o.detail.forms.length"
                                                    value="暂无详情"
                                                    top="0"
                                                    size="small"
                                                ></abc-content-empty>

                                                <template v-else>
                                                    <div v-for="(m, n) in o.detail.forms" :key="n">
                                                        <div class="pharmacy-operation-title">
                                                            {{ m.sourceFormTypeName }}
                                                        </div>

                                                        <div
                                                            :class="['pharmacy-operation-content', {
                                                                'is-chinese': m.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_CHINESE
                                                            }]"
                                                        >
                                                            <div v-for="(p, q) in m.operationFormItems" :key="q" class="pharmacy-operation-item">
                                                                <span class="pharmacy-operation-item-name">{{ p.name }}</span>

                                                                <span>{{ handleOperationCount(p, o.detail.operationType, m.sourceFormType) }}</span>
                                                            </div>
                                                        </div>

                                                        <div v-if="m.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_CHINESE" class="pharmacy-operation-custom-item">
                                                            {{ handleChineseCustomOperation(m, o.detail.operationType) }}
                                                        </div>
                                                    </div>
                                                </template>
                                            </template>

                                            <div v-else v-html="o.detailContent"></div>
                                        </div>

                                        <abc-link
                                            slot="reference"
                                            size="small"
                                            :style="{
                                                marginLeft: o.content ? '8px' : '0'
                                            }"
                                        >
                                            详情
                                        </abc-link>
                                    </abc-popover>
                                </abc-flex>
                            </abc-flex>
                        </abc-flex>
                    </abc-flex>
                </abc-form>
            </div>
        </abc-container-center-main-content>
        <!--退药弹出窗-->
        <refund-dialog
            v-if="showRefund"
            v-model="showRefund"
            :dispense-id="dispenseId"
            :dispensing-forms="dispensingForms"
            :can-part-un-dispense="canPartUnDispense"
            :chinese-medicine-undispense-type="chineseMedicineUndispenseType"
            @confirm="refundConfirm"
        >
        </refund-dialog>
        <!--退药弹出二次确认窗-->
        <refund-confirm-dialog
            v-if="showRefundConfirm"
            v-model="showRefundConfirm"
            :list="refundConfirmContent"
            @confirm="handleRefundConfirm"
        >
        </refund-confirm-dialog>
        <!--库存不足弹出框-->
        <abc-dialog v-if="dialogVisible" v-model="dialogVisible">
            <div class="dialog-content clearfix">
                <h5>以下药品库存不足：</h5>
                <p>
                    <span>{{ shortageMedicineTips }}</span>
                </p>
                <p>请退费后再发药。</p>
            </div>
            <div slot="footer" class="dialog-footer">
                <abc-button @click="dialogVisible = false">
                    关闭
                </abc-button>
            </div>
        </abc-dialog>

        <select-print-dialog
            v-if="showSelectPrint"
            v-model="showSelectPrint"
            :tag-forms="tagForms"
            :is-direct="source === 3"
            type="pharmacy"
            @confirm="selectPrintConfirm"
        >
        </select-print-dialog>
        <!--        &lt;!&ndash;打印执行单&ndash;&gt;-->
        <!--        <PrintAdvise v-if="printData && printData.executeForms"-->
        <!--                     :key="'_advise' + $route.params.id"-->
        <!--                     :printData="printData"-->
        <!--                     :clinicConfig="clinicConfig"-->
        <!--                     :printPrescriptionConfig="printPrescriptionConfig"-->
        <!--                     :type="1"-->
        <!--                     ref="_advise">-->
        <!--        </PrintAdvise>-->

        <pharmacy-settings v-if="showSettingsDialog" v-model="showSettingsDialog"></pharmacy-settings>
        <confirm-pharmacy-dialog
            v-if="showDialog"
            v-model="showDialog"
            :submit="submit"
            :patient="patient"
            :dispense-id="dispenseId"
            :diagnose="diagnose"
            :dispensing-forms="dispensingForms"
            :trace-code-dispense-items="traceCodeDispenseItems"
            :trace-code-filter-item-status="traceCodeFilterItemStatus"
            :trace-code-dispensed-items="traceCodeDispensedItems"
            :form-info="postData.dispensingForms"
            :can-set-trace-code="traceCodeCollectionCheck"
            :patient-order-id="patientOrderId"
            :is-enable-trace-temp-save="isEnableTraceTempSave"
            :is-disabled-collection="isDisabledCollection"
            @print="printHandler"
            @printMedicineTag="meanwhilePrintMedicineTag"
        ></confirm-pharmacy-dialog>

        <re-dispense
            v-if="reDispenseVisible"
            :id="dispenseId"
            v-model="reDispenseVisible"
            @refresh="fetchDetail"
        ></re-dispense>
    </div>
</template>

<script type="text/ecmascript-6">
    import PatientSection from 'views/layout/patient/patient-section/index.vue';
    import PrintPopper from 'views/print/popper';
    import { getAbcPrintOptions } from '@/printer/print-handler';
    import { SourceFormTypeEnum } from '@/service/charge/constants';
    import {
        DELIVERED,
        dispenseItemStatusEnum,
        dispenseStatusEnum,
        flatArray,
        getTraceCodeDispensedItems, getTraceCodeDispensedItemsWithCollCheckStrictMode,
        getTraceCodeDispensingItems,
        payStatusEnum,
        PROCESSED,
        SEND_MEDICINE,
        UPDATE_DELIVERY,
        WITHDRAWAL_MEDICINE,
    } from './constants';

    import FormTable from './table/index.vue';
    import ConfirmPharmacyDialog from 'views/pharmacy/confirm-pharmacy-dialog';
    // import DeliveryInfo from './delivery-info';
    import DispenserSelector from './dispenser-selector/dispenser-selector.vue';
    import RefundDialog from './refund-dialog.vue';
    import RefundConfirmDialog from './refund-confirm-dialog.vue';
    import SelectPrintDialog from '../cashier/select-print-dialog.vue';
    import PharmacySettings from './pharmcy-settings/pharmacy-settings.vue';
    // import AirPharmacyTag from 'src/views/layout/air-pharmacy-tag/index.vue';
    import DispenseAPI from 'api/dispensary';
    import PrintAPI from 'api/print';
    import ClinicAPI from 'api/clinic';
    import TpsAPI from 'api/tps';

    import {
        formatGoodsName, getSpec,
    } from 'src/filters/index';
    import { mapGetters } from 'vuex';
    import { isShortage } from 'utils/validate';
    import clone from 'utils/clone';
    import Clone from 'utils/clone';
    import localStorage from 'utils/localStorage-handler';

    import { PROCESS_TYPES_ENUM } from 'views/settings/dispense-setting/config';

    import Service from './service';
    import Advise from './advise';

    import DispensaryAPI from 'api/dispensary.js';
    import AbcPrinter from '@/printer/index.js';
    import { OutpatientChargeTypeEnum } from 'views/outpatient/constants.js';
    import { PharmacyTypeEnum } from 'views/common/enum.js';
    import {
        ABCPrintConfigKeyMap, PrintMode,
    } from '@/printer/constants';
    import {
        chargeIsRetail, getWarnChargeItemGroup,
    } from 'views/cashier/utils/index.js';
    import {
        MODULE_ID_MAP, RELATIVE_STATUS,
    } from 'utils/constants.js';
    import Big from 'big.js';
    import ReDispense from 'views/pharmacy/re-dispense.vue';
    import PharmacyCallingAPI from 'api/pharmacy-call';
    import CollectionTraceCodeDialog from '@/service/trace-code/dialog-collection-trace-code';
    import { CollectionTraceCodeCheck } from 'views/settings/trace-code/constants';
    import { parseTime } from '@/utils';
    import TraceCode, {
        SceneTypeEnum, TraceCodeScenesEnum,
    } from '@/service/trace-code/service';
    import { SelectPrintFunctionalDialog } from 'views/cashier/select-print-dialog';
    import {
        ChargeBusinessSceneEnum, LockBusinessKeyEnum,
    } from '@/common/constants/business-lock';
    import PatientOrderLockService from 'utils/patient-order-lock-service';
    import Logger from 'utils/logger';
    import AbcSocket from 'views/common/single-socket';
    import {
        GoodsSubTypeEnum, GoodsTypeEnum,
    } from '@abc/constants/src';
    import PharmacyBatchesConfirmDialog from 'views/pharmacy/pharmacy-batches-confirm';

    const PrintConfigDialogModule = () => import('@/printer/components/print-config-dialog');

    const PrecriptionPrintType = Object.freeze({
        NOMAL_PR: 0, // 一般处方
        IMG_PR: 1, // 拍照续方
        HISTORY_PR: 2, // 历史续方
    });

    const DispenseStatus = Object.freeze({
        DISPENSE_BY_USER: 0, // 一般发药
        DISPENSE_BY_AUTO: 1, // 自动发药
        DISPENSE_BY_OPENAPI: 10, // 历史续方
    });

    export default {
        name: 'DispenseDetail',
        components: {
            PatientSection,
            FormTable,
            RefundDialog,
            RefundConfirmDialog,
            SelectPrintDialog,
            PrintPopper,
            DispenserSelector,
            Service,
            Advise,
            PharmacySettings,
            ConfirmPharmacyDialog,
            ReDispense,
        },
        filters: {
            resolvePharmacyStatusName(status) {
                const statusMap = {
                    [dispenseItemStatusEnum.CANCELED]: '已关闭',
                    [dispenseItemStatusEnum.DISPENSED]: '已发药',
                    [dispenseItemStatusEnum.RETURN]: '已退药',
                    [dispenseItemStatusEnum.WAITING]: '待发药',
                    [dispenseItemStatusEnum.PART]: '待发药',
                };
                return statusMap[+status];
            },
        },
        provide() {
            return {
                detailProvide: this.detailProvide,
            };
        },
        data() {
            return {
                dispenseStatusEnum,
                employees: [],
                patient: {
                    id: null,
                    name: '',
                    sex: '男',
                    age: {
                        year: null,
                        month: null,
                    },
                    mobile: '',
                    isMember: null,
                },

                printOpt: {
                    printSelect: '标签',
                    finishSelect: [],
                },
                status: 0,
                isPartDispensed: 0, // 1 部分发药
                chineseMedicineUndispenseType: 0,
                dispensingForms: [],

                postData: {
                    dispensingForms: [],
                    deliveryCompanyId: '',
                    deliveryOrderNo: '',
                    dispensedByIds: [],
                },
                diagnose: '',
                printData: {},
                loading: true,
                buttonLoading: false,
                showRefund: false,
                showRefundConfirm: false,
                refundConfirmContent: [],
                currentRefundData: {},
                dialogVisible: false,
                shortageMedicines: [],
                refundData: [],
                isShortage,
                showSelectPrint: false,
                printAdviceTagLoading: false,
                isFirstPrintAdviceTag: true,
                isFirstPrintDispensing: true,
                printDispensingLoading: false,
                isFirstPrintPatientTag: false,
                printPatientTagLoading: false,
                pharmacyDispenseFlag: false,
                patientOrderId: '',
                sourceSheetId: '',
                source: 0, // 就诊单来源 1. 挂号 2:门诊 3：零售销售 4: 代录处方
                sourceSheetType: 0, // 等同收费处的type
                dispensedByEmployee: [], //多人发药list 最多支持3人
                dispensedByName: '',
                printable: null,
                executedType: 1,
                sellerDepartmentName: '',
                sellerName: '',

                deliveryType: 0,
                deliveryInfo: {},
                deliveredStatus: 0,

                doctorAdvice: null,
                dispensingSheetOperationRecordRsp: null,
                recordUnfold: true,
                showSettingsDialog: false,
                // 下级组件使用
                detailProvide: {
                    isVirtualPharmacy: false,
                },
                otherDispensingSheets: [],
                serviceKey: 'serviceKey',// 药事服务key
                showDialog: false, //展示弹窗
                curPharmacyName: '',
                curPharmacy: '',//当前药房

                valueList: [],
                SourceFormTypeEnum,

                canUnDispense: 0, // 是否允许退药 - 由后端返回字段决定
                reDispenseVisible: false, // 重新发药弹窗
                supportReDispense: 0,
                canPartUnDispense: true,
                canReReportTraceCode: false, //是否支持补录追溯码

                dispenseId: '',
                medicineTagPrintData: {}, // 打印用药标签需要的数据

                airPharmacyOrderId: '', // 空中药房订单id

                callLoading: false, // 呼叫取药按钮loading
                dispensingCallingItemId: '', // 是否展示呼叫取药按钮

                lockedInfo: null, // 锁单信息

                payStatus: payStatusEnum.PAY,

                dispensedTime: '', // 发药时间
                shebaoCardInfo: null,

                isShebaoPay: 0,
            };
        },
        computed: {
            ...mapGetters('viewDistribute',[
                'viewDistributeConfig',
            ]),
            ...mapGetters([
                'printMedicalDocumentsConfig',
                'isCanSeePatientMobileInPharmacy',
                'traceCodeConfig',
                'clinicBasicConfig',
                'dispensingConfig',
            ]),
            traceCodeCollectionCheck() {
                return this.traceCodeConfig?.collectionCheck || 0;
            },
            //是否启用采集强控制模式
            hasEnableCollCheckStrictMode() {
                return TraceCode.hasEnableCollCheckStrictMode;
            },
            //是否启用追溯码暂存功能 不校验追溯码
            isEnableTraceTempSave() {
                return this.hasEnableCollCheckStrictMode || !!TraceCode.requireCollectTraceCode;
            },
            // 可填写追溯码的items
            traceCodeDispenseItems() {
                return getTraceCodeDispensingItems(this.dispensingForms);
            },
            traceCodeFilterItemStatus() {
                return [
                    dispenseItemStatusEnum.WAITING,
                    dispenseItemStatusEnum.PART,
                ];
            },
            // 已经发药的列表
            traceCodeDispensedItems() {
                const {
                    formItems,returnedFormItems,
                } = getTraceCodeDispensedItems(this.dispensingForms, [dispenseItemStatusEnum.DISPENSED,dispenseItemStatusEnum.RETURN]);
                const traceCodeDispensedItems = [...formItems, ...returnedFormItems];
                // 旧：开启采集强控-发药前必须采集
                // 新：所有模式下都是发药前必须采集
                if (traceCodeDispensedItems && traceCodeDispensedItems.length) {
                    return getTraceCodeDispensedItemsWithCollCheckStrictMode(this.traceCodeDispenseItems,traceCodeDispensedItems);
                }
                return traceCodeDispensedItems;
            },
            isDisabledCollection() {
                // 旧：开启采集强控-发药前必须采集
                // return this.hasEnableCollCheckStrictMode ? !!(this.traceCodeDispensedItems && this.traceCodeDispensedItems.length) : this.status >= dispenseStatusEnum.DISPENSED;
                // 新：所有模式下都是发药前必须采集
                return !!(this.traceCodeDispensedItems && this.traceCodeDispensedItems.length);
            },
            showSellerInput() {
                return this.viewDistributeConfig.Pharmacy.showSellerInput;
            },
            employeeAll() {
                // 已发状态下，不能修改时的回显数据处理
                if (this.status) {
                    const list = Clone(this.employees);
                    const map = new Map();

                    list.forEach((item) => {
                        map.set(item.employeeId, item);
                    });
                    this.dispensedByEmployee.forEach((item) => {
                        // 将不在列表中的人加入，以回显数据
                        if (!map.has(item.id)) {
                            list.push({
                                employeeId: item.id,
                                employeeName: item.name,
                            });
                        }
                    });

                    return list;
                }

                return this.employees;
            },
            employeeActive() {
                return this.employees.filter((e) => e.status === RELATIVE_STATUS.ACTIVE);
            },
            departmentSellerStr() {
                const _arr = [];
                if (this.sellerDepartmentName) {
                    _arr.push(this.sellerDepartmentName);
                }
                if (this.sellerName) {
                    _arr.push(this.sellerName);
                }
                return _arr.join('-');
            },
            formatRecord() {
                if (!this.dispensingSheetOperationRecordRsp) return [];

                const type = {
                    0: '加工',
                    1: '退药',
                    2: '发药',
                    3: '审核',
                    4: '快递发货',
                    5: '修改发货信息',
                    6: '收费',
                    7: '取消快递',
                    8: '取消加工',
                    9: '调配',
                    12: '撤销审核',
                    13: '撤销调配',
                    15: '关闭',
                    16: '重新打开',
                    17: '退费',
                };

                return this.dispensingSheetOperationRecordRsp.map((o) => {
                    let content = o.content || '';
                    const typeText = type[o.operationType];
                    if (o.operationType === UPDATE_DELIVERY) {
                        content = '';
                    }
                    if ([PROCESSED].includes(o.operationType)) {
                        content += `；${typeText}人（${o.operatorName}）`;
                    }
                    return {
                        date: this.formatDate(o.created),
                        type: typeText,
                        name: o.createdBy,
                        content,
                        showDetail: [UPDATE_DELIVERY, DELIVERED, WITHDRAWAL_MEDICINE, SEND_MEDICINE].includes(o.operationType),
                        detailContent: o.detailContent || '暂无详情',
                        detail: o.detail,
                        isFormDetail: [WITHDRAWAL_MEDICINE, SEND_MEDICINE].includes(o.operationType),
                    };
                });
            },

            formatReview() {
                return this.getPharmacyServe('auditedStatus');
            },
            formatCompounded() {
                return this.getPharmacyServe('compoundedStatus');
            },

            formatOrderReview() {
                return this.getPharmacyServeOrder(this.formatReview);
            },
            formatOrderCompounded() {
                return this.getPharmacyServeOrder(this.formatCompounded);
            },

            formatProcess() {
                const result = this.dispensingForms.filter((o) => o.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_CHINESE && o.processedStatus !== 0)
                    .map((m) => {
                        if (m.usageInfo && m.usageInfo.processUsage) {
                            // 收费处换了字段，一剂几袋用processBagUnitCountDecimal，老数据用processBagUnitCount
                            const processBagUnitCount = m.usageInfo?.processBagUnitCountDecimal || m.usageInfo.processBagUnitCount;
                            const totalProcessCount = m.usageInfo?.totalProcessCount || Math.ceil(m.usageInfo.doseCount * processBagUnitCount);
                            const takeMedicationTime = this.dispensingConfig.isTakeMedicationTime && m.takeMedicationTime ?
                                `取药时间：${parseTime(m.takeMedicationTime, 'y-m-d')} ${parseTime(m.takeMedicationTime, 'h:i', true)}` : '';
                            return {
                                label: `（${m.usageInfo.processUsage}` +
                                    `${m.usageInfo.usageType === PROCESS_TYPES_ENUM.DECOCTION ? `，
                                    ${processBagUnitCount}袋/剂，共${totalProcessCount}袋` : ''}）`,
                                id: m.id,
                                processedStatus: m.processedStatus,
                                alias: '中药处方',
                                takeMedicationTimeStr: takeMedicationTime,
                                takeMedicationTime: m.takeMedicationTime, // 取药时间
                            };
                        }

                        return {
                            label: '',
                            id: m.id,
                            processedStatus: m.processedStatus,
                        };
                    })
                    .filter((n) => !!n.label);

                if (result.length > 1) {
                    result.forEach((o, i) => {
                        const index = this.formatOrderReview.findIndex((item) => item.id === o.id);
                        if (index === -1) {
                            o.alias = `中药处方${this.sectionToChinese(i + 1)}`;
                        } else {
                            o.alias = this.formatOrderReview[index].alias;
                        }
                    });
                }

                return result;
            },
            isWholeBillCharge() {
                return !!this.dispensingConfig.wholeSheetOperateEnable;
            },
            reviewConfig() {
                return this.$store.state.config.dispensingConfig.prescriptionReview;
            },
            compoundedConfig() {
                return this.$store.state.config.dispensingConfig.prescriptionCompound;
            },
            isCustomization() {
                return this.pharmacyDispenseFlag === DispenseStatus.DISPENSE_BY_OPENAPI;
            },
            // 发药前是否选中药品
            isSelectDrugBeforeDispensing() {
                if (this.disableByLock) return false;
                if (this.isCustomization) {
                    return false;
                }
                let isSelect = false;
                if (this.postData.dispensingForms.length) {
                    this.postData.dispensingForms.forEach((item) => {
                        if (item.dispensingFormItems.length > 0) {
                            isSelect = true;
                        }
                    });
                }
                return isSelect;
            },
            // 已发药
            isDispensing() {
                return this.status === dispenseStatusEnum.DISPENSED;
            },
            // 已退费
            isRefundFee() {
                return this.status === dispenseStatusEnum.RETURN;
            },

            // 收费单状态：已退, 有审核/调配/发药记录包括已撤销的记录, 发药单为关闭
            // https://www.tapd.cn/22044681/prong/stories/view/1122044681001070761
            isRefunded() {
                return this.status === dispenseStatusEnum.CLOSE && this.payStatus === payStatusEnum.REFUND;
            },
            //是否为代煎中心
            isDaijianCenter() {
                return this.pharmacyList.find((pharmacyItem) => {
                    return this.curPharmacy === pharmacyItem.no && pharmacyItem.externalPharmacyConfig?.forceOpenAuditAndCompound;
                });
            },
            isClose() {
                return this.status === dispenseStatusEnum.CLOSE;
            },

            shortageMedicineTips() {
                return this.shortageMedicines.map((it) => (it.cadn || it.itemName)).join('、');
            },
            ...mapGetters([
                'pharmacy',
                'clinicConfig',
                'printPrescriptionConfig',
                'printDispensingConfig',
                'westernMedicineConfig',
                'isOpenMp',
                'currentClinic',
                'userInfo',
                'multiPharmacyCanUse',
                'pharmacyList',
                'dispensingConfig',
                'isEnableDecoctionCraftCard',
            ]),
            ...mapGetters('viewDistribute', [
                'viewDistributeConfig',
            ]),

            selectedPatient() {
                return this.pharmacy.selectedPatient;
            },
            tipContent() {
                if (this.disableByLock) return '本单正在退费中';
                return this.isCustomization ? '此药房由代煎中心发药' : '没有选择药品';
            },
            isPrintIncludeTreatment() {
                return this.printPrescriptionConfig.infusionExecute.includeTreatment;
            },
            printOptions() {
                const printOptions = [
                    {
                        value: this._printOptions.DISPENSING_ORDER.label,
                        disabled: this.disabledPrintMedicine,
                        tips: '没有药品',
                    }, {
                        value: this._printOptions.PRESCRIPTION.label,
                        disabled: this.disabledPrintPR,
                        tips: `没有${this._printOptions.PRESCRIPTION.label}`,
                    }, {
                        value: this._printOptions.INFUSION_EXECUTE.label,
                        disabled: this.disabledExecuteTransfusionSheet,
                        tips: `没有${this._printOptions.INFUSION_EXECUTE.label}`,
                    }, {
                        value: this._printOptions.MEDICINE_TAG.label,
                        disabled: this.disabledPrintAdviceTag,
                        tips: '没有标签',

                    }, {
                        value: this._printOptions.PATIENT_TAG.label,
                        disabled: this.disabledPrintPatientTag,
                        tips: '匿名患者没有患者信息',
                    },
                ];
                if (this.isEnableDecoctionCraftCard) {
                    printOptions.push({
                        value: this._printOptions.DECOCTION_CRAFT_CARD.label,
                        hidden: this.disabledDecoctionCraftCard,
                        tips: '没有煎药工艺卡',
                    });
                } if (!this.disabledPrintUndispensing) {
                    printOptions.splice(2, 0, {
                        value: this._printOptions.UNDISPENSING_ORDER.label,
                        tips: '没有退药',
                    });
                }
                return printOptions;
            },
            disabledPrintAdviceTag() {
                return this.printable && !this.printable.medicineTag;
            },
            // 打印输注执行单 只有输液处方
            disabledExecuteTransfusionSheet() {
                if (this.isPrintIncludeTreatment) {
                    return this.printable && !this.printable.executeInfusionSheet;
                }
                return this.printable && !this.printable.executeTransfusionSheet;
            },
            disabledPrintPR() {
                // 零售销售---- 直接收费
                return this.printable && !this.printable.prescription;
            },
            // 打印发药单
            disabledPrintMedicine() {
                return this.printable && !this.printable.dispensingSheet;
            },
            disabledPrintPatientTag() {
                return this.printable && !this.printable.patientTag;
            },
            disabledDecoctionCraftCard() {
                return this.printable && !this.printable.decoctionCraftCard;
            },
            disabledPrintUndispensing() {
                return this.printable && !this.printable.undispenseSheet;
            },
            tagForms() {
                let forms = [];
                const isSale = chargeIsRetail(this.sourceSheetType);
                const tagTypes = isSale ? [ SourceFormTypeEnum.PRESCRIPTION_CHINESE] :
                    [ SourceFormTypeEnum.PRESCRIPTION_CHINESE,
                      SourceFormTypeEnum.PRESCRIPTION_WESTERN,
                      SourceFormTypeEnum.PRESCRIPTION_INFUSION ];
                if (this.dispensingForms) {
                    const newForms = clone(this.dispensingForms);
                    forms = newForms.filter((form) => {
                        if (tagTypes.includes(form.sourceFormType)) {
                            form.formItems = form.dispensingFormItems && form.dispensingFormItems.filter((item) => {
                                if (item.status === dispenseItemStatusEnum.WAITING || item.status === dispenseItemStatusEnum.PART ||
                                    (item.status === dispenseItemStatusEnum.DISPENSED && !item.partDispense)) {
                                    item.checked = true;
                                    return item;
                                }
                            });
                            delete form.dispensingFormItems;
                            form.isFullChecked = true;
                            if (form.formItems.length > 0) {
                                return form;
                            }
                        }
                    });
                }
                return forms;
            },

            deliveryInfoStr() {
                const {
                    addressCityName,
                    addressDetail,
                    addressDistrictName,
                    addressProvinceName,
                    deliveryName,
                    deliveryMobile,
                } = this.deliveryInfo || {};
                let str = '';
                if (deliveryName) {
                    str += deliveryName;
                }
                if (deliveryMobile) {
                    str += `${(str ? '/' : '') + deliveryMobile}/`;
                }
                str += addressProvinceName || '';
                str += addressCityName || '';
                str += addressDistrictName || '';
                str += addressDetail || '';
                return str;
            },

            vendorName() {
                if (this.detailProvide.isVirtualPharmacy) {
                    return this.dispensingForms && this.dispensingForms.length > 0 && this.dispensingForms[0].vendorName || '';
                }
                return '';
            },
            isReplaceDecoct() {
                return this.dispensingForms && this.dispensingForms.length > 0 && this.dispensingForms[0].medicineStateScopeId === 10;// 9自煎10代煎
            },

            disableByLock() {
                const {
                    businessKey, value,
                } = this.lockedInfo || {};
                const { businessScene } = value || {};
                if (
                    businessKey === LockBusinessKeyEnum.CHARGE &&
                    businessScene !== ChargeBusinessSceneEnum.CHARGE_SHEET_REFUND
                ) return false;
                return !!this.lockedInfo;
            },
            // 禁止跨月退药的地区
            disabledRefundRegion() {
                return this.$abcSocialSecurity.isOpenSocial && (this.$abcSocialSecurity.config?.isHeilongjiang ||
                    this.$abcSocialSecurity.config?.isNeimenggu ||
                    this.$abcSocialSecurity.config?.isJilin);
            },
        },
        watch: {
            // 如果路由有变化，会再次执行该方法
            '$route': function() {
                this.fetchDetail();
            },
        },
        created() {
            this.$store.dispatch('fetchPrintMedicalDocumentsConfig');
            this._printOptions = this.viewDistributeConfig.Print.printOptions;
            this.fetchDetail();
            this._key = `${this.currentClinic.clinicId}_${this.userInfo.id}`;
            this.fetchEmployeeByModuleId();

            this._handleSocketLock = (data) => {
                this.onLockSocket(data, 'lock');
            };
            this._handleSocketUnlock = (data) => {
                this.onLockSocket(data, 'unlock');
            };
            // socket监听
            const { socket } = AbcSocket.getSocket();
            this._socket = socket;
            this._socket.on('patientOrder.sheet_lock', this._handleSocketLock);
            this._socket.on('patientOrder.sheet_unlock', this._handleSocketUnlock);
        },
        beforeDestroy() {
            this._socket.off('patientOrder.sheet_lock', this._handleSocketLock);
            this._socket.off('patientOrder.sheet_unlock', this._handleSocketUnlock);
        },
        methods: {
            getSpec,
            formatGoodsName,
            async printMedicineTagHandler() {
                try {
                    this.loading = true;
                    const { dispenseId } = this;
                    if (!dispenseId) return;
                    const res = await DispenseAPI.fetchDetail(dispenseId, true);
                    const { data: sheet } = res;
                    const { dispensingSheet: data } = sheet;

                    const { dispensingForms } = data;

                    let forms = [];
                    const isSale = chargeIsRetail(data.sourceSheetType);
                    const tagTypes = isSale ? [ SourceFormTypeEnum.PRESCRIPTION_CHINESE] :
                        [ SourceFormTypeEnum.PRESCRIPTION_CHINESE,
                          SourceFormTypeEnum.PRESCRIPTION_WESTERN,
                          SourceFormTypeEnum.PRESCRIPTION_INFUSION ];
                    if (dispensingForms && dispensingForms.length) {
                        const newForms = clone(dispensingForms);
                        forms = newForms.filter((form) => {
                            if (tagTypes.includes(form.sourceFormType)) {
                                form.formItems = form.dispensingFormItems && form.dispensingFormItems.filter((item) => {
                                    if (item.status === dispenseItemStatusEnum.WAITING || item.status === dispenseItemStatusEnum.PART ||
                                        (item.status === dispenseItemStatusEnum.DISPENSED && !item.partDispense)) {
                                        item.checked = true;
                                        return item;
                                    }
                                    return false;
                                });
                                delete form.dispensingFormItems;
                                form.isFullChecked = true;
                                if (form.formItems.length > 0) {
                                    return form;
                                }
                                return false;
                            }
                            return false;
                        });
                    }

                    const onConfirm = async (params) => {
                        const { selectedData } = params;
                        const medicineForms = selectedData.w.concat(selectedData.c);

                        const {
                            doctorName, patientOrderNo, patient,departmentName,
                        } = data;
                        const printOptions = getAbcPrintOptions('用药标签', {
                            forms: medicineForms,
                            patient,
                            doctorName,
                            patientOrderNo,
                            clinicName: this.currentClinic.clinicName,
                            departmentName,
                            // 手动打印,不做自动打印筛选
                            isManualPreview: true,
                        });
                        AbcPrinter.abcPrint(printOptions);
                    };

                    if (forms.length) {
                        await new SelectPrintFunctionalDialog({
                            value: true, tagForms: forms, onConfirm,
                        }).generateDialogAsync({ parent: this });
                    }
                } catch (e) {
                    console.error('打印用药标签失败\n', e);
                } finally {
                    this.loading = false;
                }
            },
            async changeVendor(vender) {
                if (vender.vendorId !== this.dispensingForms[0].vendorId) {
                    this.dispensingForms = this.dispensingForms.map((item) => {
                        return {
                            ...item,
                            vendorId: vender.vendorId,
                            vendorName: vender.vendorName,
                        };
                    });
                    try {
                        const res = await DispenseAPI.updateVendorInfo(this._id, {
                            vendorId: vender.vendorId,
                            vendorName: vender.vendorName,
                            dispenseFormId: this.dispensingForms[0].id || '',
                        });
                        if (res) {
                            // 修改供应商后需要筛选详情信息
                            this.$nextTick(() => {
                                this.fetchDetail();
                            });
                        }
                    } catch (e) {
                        console.log(e);
                    }
                }

            },
            async openPrintConfigSettingDialog() {
                const { default: PrintConfigDialog } = await PrintConfigDialogModule();
                new PrintConfigDialog({ scene: 'pharmacy' }).generateDialogAsync({ parent: this });
            },

            // 重新发药需要清空 traceableCodeList
            initTraceableCodeList() {
                this.dispensingForms.forEach((form) => {
                    form.dispensingFormItems.forEach((item) => {
                        item.traceableCodeList = [];
                        item.composeChildren?.forEach((child) => {
                            child.traceableCodeList = [];
                        });
                    });
                });
            },
            async reDispense() {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '该发药单已全部退药，是否确定打开发药单并重新发药?',
                    onConfirm: async () => {
                        try {
                            const { data } = await DispenseAPI.checkCanDispense(this._id);
                            const {
                                dispensingSheetCalculateResult, reDispenseWithBatchInfo,
                            } = data;

                            if (!reDispenseWithBatchInfo || (dispensingSheetCalculateResult && dispensingSheetCalculateResult.reDispenseByOriginBatch)) {
                                await DispenseAPI.reDispense(this._id, dispensingSheetCalculateResult?.dispensingForms || null);

                                await this.fetchDetail();
                                /**
                                 * 【hotfix-g【Bug转需求】重新收费/发药，需要加载之前前采集的追溯码】
                                 * https://www.tapd.cn/tapd_fe/22044681/story/detail/1122044681001092206
                                 * 重新发药后不清空已采集的追溯码
                                 */
                                // this.initTraceableCodeList();
                                return;
                            }

                            this.reDispenseVisible = true;
                        } catch (e) {
                            this.$Toast({
                                message: e.message,
                                type: 'error',
                            });
                        }
                    },
                });
            },

            handleOperationCount(item, type, formType) {
                // type 1退药 2发药
                const {
                    unitCount, doseCount, unit,
                } = item;

                if (formType === SourceFormTypeEnum.PRESCRIPTION_CHINESE) {
                    return `${type === 2 ? '' : '-'}${unitCount}${unit}`;
                }

                return `${type === 2 ? '' : '-'}${Big(unitCount).times(Big(doseCount)).toNumber()}${unit}`;
            },

            handleChineseCustomOperation(items, type) {
                const doseCount = items?.operationFormItems?.[0]?.doseCount ?? 0;

                return `[剂量]共${type === 2 ? '' : '退'}${doseCount}剂`;
            },

            handleChangeCount(payload) {
                const {
                    count,
                    formId,
                    formItemId,
                    childId,
                    formType,
                    dispensingFormItemBatches,
                } = payload;


                // 中药处方是改form上的值
                if (formType === SourceFormTypeEnum.PRESCRIPTION_CHINESE) {
                    const item = this.postData.dispensingForms.find((o) => o.id === formId);
                    if (item) {
                        item.doseCount = count;
                    }

                    const form = this.dispensingForms.find((o) => o.id === formId);
                    if (form) {
                        form._remainingDoseCount = count || form.remainingDoseCount;

                        form.dispensingFormItems.forEach((o) => {
                            if (o.status === dispenseItemStatusEnum.WAITING || o.status === dispenseItemStatusEnum.PART) {
                                o.doseCount = count || o._doseCount;
                                o.totalCount = count ? Big(o.doseCount).times(Big(o.unitCount)).toNumber() : o._totalCount;
                                o.totalPrice = count ? Big(o.totalCount).times(Big(o.unitPrice)).toNumber() : o._totalPrice;
                                o.traceableCodeList = o.traceableCodeList?.filter((it) => !it.used);
                            }
                        });
                    }

                    return;
                }

                const valueForm = this.valueList.find((o) => o.id === formId); // 更改valueList的值
                const oriForm = this.dispensingForms.find((o) => o.id === formId); // 更改dispense v-model的值
                if (!valueForm) return;

                // *** find只会找第一个
                const valueItem = valueForm.dispensingFormItems.find((m) => m.id === formItemId);
                const oriItem = oriForm.dispensingFormItems.find((m) => m.id === formItemId);
                if (!valueItem) return;

                if (formType === SourceFormTypeEnum.COMPOSE) {
                    const valueChild = valueItem.composeChildren.find((n) => n.id === childId);
                    const oriChild = oriItem.composeChildren.find((n) => n.id === childId);

                    if (!valueChild) return;

                    valueChild.unitCount = count;
                    valueChild.dispensingFormItemBatches = dispensingFormItemBatches;
                    oriChild.modelRemainingUnitCount = count;
                    oriChild._dispensingFormItemBatches = dispensingFormItemBatches;
                    // 旧：修改数量需要清空已经录入的追溯码 开启了采集强控不清空
                    // 新：所有模式下都不清空
                    // if (!this.hasEnableCollCheckStrictMode) {
                    //     oriChild.traceableCodeList = oriChild.traceableCodeList?.filter((it) => it.used);
                    // }
                    return;
                }

                valueItem.unitCount = count;
                valueItem.dispensingFormItemBatches = dispensingFormItemBatches;

                oriItem.modelRemainingUnitCount = count;
                oriItem._dispensingFormItemBatches = dispensingFormItemBatches;
                // 旧：修改数量需要清空已经录入的追溯码 开启了采集强控不清空
                // 新：所有模式下都不清空
                // if (!this.hasEnableCollCheckStrictMode) {
                //     oriItem.traceableCodeList = oriItem.traceableCodeList?.filter((it) => it.used);
                // }
            },

            sectionToChinese(section) {
                const chnNumChar = ['零','一','二','三','四','五','六','七','八','九'];
                const chnUnitChar = ['','十','百','千','万','亿','万亿','亿亿'];
                let strIns = '', chnStr = '';
                let unitPos = 0;
                let zero = true;
                while (section > 0) {
                    const v = section % 10;
                    if (v === 0) {
                        if (!zero) {
                            zero = true;
                            chnStr = chnNumChar[v] + chnStr;
                        }
                    } else {
                        zero = false;
                        strIns = chnNumChar[v];
                        strIns += chnUnitChar[unitPos];
                        chnStr = strIns + chnStr;
                    }
                    unitPos++;
                    section = Math.floor(section / 10);
                }
                return chnStr;
            },
            formatDate(time) {
                const date = new Date(time);

                const y = date.getFullYear();
                let m = date.getMonth() + 1;
                m = m < 10 ? (`0${m}`) : m;
                let d = date.getDate();
                d = d < 10 ? (`0${d}`) : d;
                let h = date.getHours();
                h = h < 10 ? (`0${h}`) : h;
                let minute = date.getMinutes();
                minute = minute < 10 ? (`0${minute}`) : minute;

                return `${y}-${m}-${d} ${h}:${minute}`;
            },

            getPharmacyServe(type) {
                return this.dispensingForms.reduce((res, o) => {

                    if (o[type] === 0) return res;
                    // *** 做了productId去重

                    if (o.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_CHINESE) {
                        const doseCount = o.doseCount || 0;
                        const totalCount = o.totalCount || 0 ;
                        const dispenseItemCount = o.dispenseItemCount || 0 ;
                        const unitCountPerDose = o.unitCountPerDose || 0 ;

                        const auditedStr = unitCountPerDose ? `，单剂 ${unitCountPerDose}g，总计${totalCount}g` : '';
                        const compoundedStr = dispenseItemCount * doseCount || 0;

                        // 中西成药处方
                        res.push({
                            label: type === 'auditedStatus' ? `（共${doseCount}剂，${dispenseItemCount}味 ${auditedStr}）` :
                                `（味数x剂数 ${compoundedStr}）`,
                            id: o.id,
                            auditedStatus: o.auditedStatus,
                            compoundedStatus: o.compoundedStatus,
                            sourceFormType: o.sourceFormType,
                        });
                    } else if (o.sourceFormType === SourceFormTypeEnum.COMPOSE) {
                        o.dispensingFormItems.forEach((m) => {
                            const kinds = [];
                            m.composeChildren.forEach((item) => {
                                if (kinds.indexOf(item.productId) === -1) {
                                    kinds.push(item.productId);
                                }
                            });

                            res.push({
                                label: `（${m.name}: ${kinds.length}种）`,
                                id: o.id,
                                auditedStatus: o.auditedStatus,
                                compoundedStatus: o.compoundedStatus,
                                sourceFormType: o.sourceFormType,
                            });
                        });
                    } else {
                        const kinds = [];
                        o.dispensingFormItems.forEach((item) => {
                            if (kinds.indexOf(item.productId) === -1) {
                                kinds.push(item.productId);
                            }
                        });

                        const dispenseItemCount = o?.relateDispensingForm?.dispenseItemCount || 0;

                        let label = '', alias = '';
                        if (o.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_WESTERN) {
                            label = `（${kinds.length + dispenseItemCount}种）`;
                        } else if (o.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_EXTERNAL) {
                            label = `（${kinds.length}种）`;
                        } else if (o.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_INFUSION) {
                            label = `（${kinds.length + dispenseItemCount}种）`;
                        } else if (o.sourceFormType === SourceFormTypeEnum.GIFT) {
                            label = `（${kinds.length}种）`;
                        } else if (o.sourceFormType === SourceFormTypeEnum.ADDITIONAL_SALE_PRODUCT_FORM ||
                            o.sourceFormType === SourceFormTypeEnum.MATERIAL) {
                            label = `（${kinds.length}种）`;
                            alias = '材料商品';
                        }

                        res.push({
                            alias,
                            label,
                            id: o.id,
                            auditedStatus: o.auditedStatus,
                            compoundedStatus: o.compoundedStatus,
                            sourceFormType: o.sourceFormType,
                            sourceFormId: o.sourceFormId,
                        });
                    }

                    return res;

                }, []);

            },

            getPharmacyServeOrder(formatArr) {
                const types = [
                    SourceFormTypeEnum.PRESCRIPTION_WESTERN,
                    SourceFormTypeEnum.PRESCRIPTION_EXTERNAL,
                    SourceFormTypeEnum.PRESCRIPTION_INFUSION,
                    SourceFormTypeEnum.PRESCRIPTION_CHINESE,
                    SourceFormTypeEnum.GIFT,
                ];

                const alias = {
                    4: '中西成药处方',
                    5: '输液处方',
                    6: '中药处方',
                    10: '赠品',
                    16: '外治处方',
                };

                return formatArr.reduce((res, cur) => {
                    if (types.includes(cur.sourceFormType)) {
                        const item = res.find((o) => o.sourceFormType === cur.sourceFormType);
                        if (item) {
                            if (item.alias === alias[cur.sourceFormType]) {
                                item.alias = `${item.alias}一`;
                            }

                            const len = res.filter((o) => o.sourceFormType === cur.sourceFormType).length;

                            res.push({
                                ...cur,
                                alias: `${alias[cur.sourceFormType]}${this.sectionToChinese(len + 1)}`,
                            });
                        } else {
                            res.push({
                                ...cur,
                                alias: alias[cur.sourceFormType],
                            });
                        }
                    }

                    return res;
                }, []) || [];
            },

            async fetchEmployeeByModuleId() {
                const { data } = await ClinicAPI.fetchEmployeeByModuleId({
                    moduleId: [MODULE_ID_MAP.pharmacy],
                    showDisable: 1,
                }, true);
                this.employees = data;
            },

            changeDispensedBy(dispensedByIds) {
                this.postData.dispensedByIds = dispensedByIds;
            },
            /**
             * desc [当患者信息修改后执行]
             */
            changePatientInfo(patientInfo) {
                this.fetchDetail(false);
                this.$store.dispatch('setSelectedPatient', {
                    type: 'pharmacy',
                    patientInfo,
                });
                this.$store.dispatch('updatePatientInfo', {
                    type: 'pharmacy',
                    info: patientInfo,
                });
            },
            async fetchDetail(loading = true) {
                this.loading = loading;
                this.isFirstPrintAdviceTag = true;
                this.isFirstPrintDispensing = true;
                this.printAdviceTagLoading = false;

                this._id = this.dispenseId = this.$route.params.id;
                const res = await DispenseAPI.fetchDetail(this._id);

                const { data: sheet } = res;
                const { dispensingSheet: data } = sheet;
                const { otherDispensingSheets = [] } = sheet;

                this.dispensingCallingItemId = data.dispensingCallingItemId || '';
                this.supportReDispense = data.supportReDispense;
                this.otherDispensingSheets = otherDispensingSheets;
                this.detailProvide.isVirtualPharmacy = data.pharmacyType === PharmacyTypeEnum.VIRTUAL_PHARMACY;
                this.sourceSheetId = data.sourceSheetId;
                this.source = data.source;
                this.sourceSheetType = data.sourceSheetType;
                this.patient = data.patient || {};
                this.diagnose = data.diagnose;
                this.status = data.status;
                this.payStatus = data.payStatus;
                this.dispensedTime = data.dispensedTime;
                this.shebaoCardInfo = data.shebaoCardInfo;
                this.isShebaoPay = data.isShebaoPay;

                this.doctorAdvice = data.doctorAdvice;
                this.isPartDispensed = data.isPartDispensed;
                this.dispensedByEmployee = data.dispensedByEmployee;
                this.dispensedByName = data.dispensedByName;
                this.pharmacyDispenseFlag = data.pharmacyDispenseFlag;
                this.postData.isDecoction = data.isDecoction;
                this.chineseMedicineUndispenseType = data.chineseMedicineUndispenseType;
                this.curPharmacyName = data.pharmacyName;
                this.curPharmacy = data.pharmacyNo;
                this.printable = data.printable;
                this.patientOrderId = data.patientOrderId;
                this.canUnDispense = data.canUnDispense;
                this.sellerDepartmentName = data.sellerDepartmentName;
                this.sellerName = data.sellerName;
                this.airPharmacyOrderId = data.airPharmacyOrderId;
                this.canPartUnDispense = !!data.canPartUnDispense;
                this.canReReportTraceCode = data.canReReportTraceCode === 1;
                this.dispensingForms = data.dispensingForms.map((form) => {
                    const checkedList = [];
                    const valueList = []; // 部分发药数据，发药的时候需要拼接此数据

                    const partDispenseItems = []; // 部分发药已发药的item, 要拼接到formItem同一层级

                    form.dispensingFormItems.forEach((item) => {
                        // 处理库存信息
                        this.updateBaseInfo(data, item);

                        // 中药编辑剂数 - 数据处理（区分展示和编辑数据）
                        if (form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_CHINESE) {
                            form._remainingDoseCount = form.remainingDoseCount || 0;
                            item._doseCount = item.doseCount;
                            item._totalCount = item.totalCount;
                            item._totalPrice = item.totalPrice;
                        }

                        // 处理部分发的form item
                        if (item.status === dispenseItemStatusEnum.PART &&
                            item.operationLogs &&
                            item.operationLogs.length > 0) {
                            this.updateBaseInfo(data, item.operationLogs[0]);
                            partDispenseItems.push({
                                id: item.id,
                                item: {
                                    ...item.operationLogs[0],
                                    partDispense: true,
                                    traceableCodeList: item.traceableCodeList,
                                },
                                compose: false,
                            });
                        }

                        // 处理checked 和 需要保存的value的值
                        if (
                            (item.status === dispenseItemStatusEnum.WAITING || item.status === dispenseItemStatusEnum.PART) &&
                            (!isShortage(item).flag || /*1是否有库存，2是否自备药（不参与划价），3是否是虚拟药房（不考虑库存），4开启了整单发退药开关 */
                                item.sourceItemType === OutpatientChargeTypeEnum.NO_CHARGE ||
                                this.detailProvide.isVirtualPharmacy ||
                                this.isWholeBillCharge)
                        ) {
                            item.checked = true;
                            item.modelRemainingUnitCount = item.remainingUnitCount; // 设置dispense v-model值，remainingUnitCount用于验证

                            checkedList.push({
                                id: item.id,
                            });

                            // 单独存dispense所修改的值(部分发药相关数据)，避免影响原来checkbox相关逻辑
                            valueList.push({
                                id: item.id,
                                unitCount: item.remainingUnitCount,
                                doseCount: item.remainingDoseCount,
                                dispensingFormItemBatches: item.dispensingFormItemBatches,
                            });
                        }

                        if (item.composeChildren) {
                            const childChecked = [];
                            const valueChildList = [];

                            item.composeChildren.forEach((child) => {
                                this.updateBaseInfo(data, child);

                                if (child.status === dispenseItemStatusEnum.PART &&
                                    child.operationLogs &&
                                    child.operationLogs.length > 0) {
                                    this.updateBaseInfo(data, child.operationLogs[0]);
                                    partDispenseItems.push({
                                        id: child.id,
                                        item: {
                                            ...child.operationLogs[0],
                                            partDispense: true, // 标注部分发
                                            traceableCodeList: child.traceableCodeList,
                                        },
                                        compose: true,
                                        parentId: item.id,
                                    });
                                }

                                if ((this.isWholeBillCharge || !isShortage(child).flag) && (child.status === dispenseItemStatusEnum.WAITING || child.status === dispenseItemStatusEnum.PART)) {
                                    child.checked = true;
                                    child.modelRemainingUnitCount = child.remainingUnitCount;
                                    childChecked.push({
                                        id: child.id,
                                    });

                                    valueChildList.push({
                                        id: child.id,
                                        unitCount: child.remainingUnitCount,
                                        doseCount: child.remainingDoseCount,
                                        dispensingFormItemBatches: child.dispensingFormItemBatches,
                                        traceableCodeList: child.traceableCodeList,
                                    });
                                }
                            });
                            Object.assign(item, { checkedList: childChecked });

                            if (item.checkedList && item.checkedList.length) {
                                const index = checkedList.findIndex((o) => o.id === item.id);

                                if (index !== -1) {
                                    checkedList.splice(index, 1, {
                                        id: item.id,
                                        composeChildren: item.checkedList,
                                    });
                                } else {
                                    checkedList.push({
                                        id: item.id,
                                        composeChildren: item.checkedList,
                                    });
                                }
                            }

                            if (valueChildList && valueChildList.length) {

                                const index = valueList.findIndex((o) => o.id === item.id);

                                if (index !== -1) {
                                    valueList.splice(index, 1, {
                                        id: item.id,
                                        composeChildren: valueChildList,
                                    });
                                } else {
                                    valueList.push({
                                        id: item.id,
                                        composeChildren: valueChildList,
                                    });
                                }
                            }
                        }
                    });

                    Object.assign(form, {
                        checkedList, valueList,
                    });

                    // 将部分发的item 拼接到 from item里面
                    if (partDispenseItems.length > 0) {
                        partDispenseItems.forEach((o) => {
                            if (o.compose) {
                                const item = form.dispensingFormItems.find((child) => child.id === o.parentId);
                                if (!item) return;

                                const index = item.composeChildren.findIndex((subChild) => subChild.id === o.id);
                                if (index < 0) return;
                                item.composeChildren.splice(index + 1, 0, o.item);
                            } else {
                                const index = form.dispensingFormItems.findIndex((child) => child.id === o.id);
                                if (index < 0) return;
                                form.dispensingFormItems.splice(index + 1, 0, o.item);
                            }
                        });
                    }

                    return form;
                });
                this.postData.dispensingForms = this.dispensingForms.map((form) => {
                    // 中药处方需要再外层拼接doseCount, 表示是按剂数发药

                    if (form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_CHINESE) {
                        return {
                            id: form.id,
                            sourceFormType: form.sourceFormType,
                            dispensingFormItems: form.checkedList,
                            doseCount: form.formSupportDispenseByDose ? form.remainingDoseCount : null,
                        };
                    }

                    return {
                        id: form.id,
                        sourceFormType: form.sourceFormType,
                        dispensingFormItems: form.checkedList,
                    };
                });

                this.medicineTagPrintData = {
                    doctorName: data.doctorName,
                    patientOrderNo: data.patientOrderNo,
                };

                this.valueList = this.dispensingForms.map((o) => {
                    return {
                        id: o.id,
                        sourceFormType: o.sourceFormType,
                        dispensingFormItems: o.valueList,
                    };
                });

                /**
                 * @desc 送药上门状态
                 * <AUTHOR>
                 * @date 2020/02/12 17:38:17
                 */
                this.deliveryType = data.deliveryType;
                this.deliveryInfo = data.deliveryInfo;
                this.deliveredStatus = data.deliveredStatus;
                if (data.deliveryInfo) {
                    this.postData.deliveryCompanyId = data.deliveryInfo.deliveryCompany &&
                        data.deliveryInfo.deliveryCompany.id || '';
                    this.postData.deliveryOrderNo = data.deliveryInfo.deliveryOrderNo;
                }

                if (this.status === dispenseStatusEnum.WAITING && !this.postData.deliveryCompanyId) {
                    this.postData.deliveryCompanyId = localStorage.getObj('delivery_company_id', this._key, true);
                }

                this.postData.dispensedByIds = [];
                // 指定发药人并且已经发过药
                if (this.dispensedByEmployee.length) {
                    this.dispensedByEmployee.forEach((item) => {
                        this.postData.dispensedByIds.push(item.id);
                    });
                } else {
                    // 未指定发药人，默认为操作人
                    this.userInfo.id && this.postData.dispensedByIds.push(this.userInfo.id);
                }

                const record = await DispenseAPI.loadRecord(this._id);

                this.dispensingSheetOperationRecordRsp = record.data.records;

                // 刷新药事服务使用的随机Key
                this.serviceKey = Math.random().toString(36).slice(2);
                await this.getLockInfo();
                this.loading = false;
            },


            updateBaseInfo(data, item) {
                // 需要对药品基础信息/库存信息进行更新
                // data.productInfos该详单里面药品的实时价格和库存
                // 根据锁库需求修改，发药校验可发数量（之前为可售数量）@picsong
                if (data.productInfos) {
                    const newMedicine = data.productInfos.find((info) => {
                        return info.id === item.productId;
                    });
                    const availablePieceCount = item.productInfo?.availablePieceCount || 0;
                    const availablePackageCount = item.productInfo?.availablePackageCount || 0;
                    // 字段还是使用可售数量，取值为可发数量
                    item.stockPieceCount = availablePieceCount;
                    item.stockPackageCount = availablePackageCount;
                    item.realProductInfo = Object.assign({}, item.productInfo, newMedicine || {});
                }
            },

            changeForms(payload) {
                const item = this.dispensingForms.find((o) => o.id === payload.id);
                if (item) {
                    item.checkedList = payload.data;
                }

                // *** checkValue
                this.postData.dispensingForms = this.dispensingForms.map((form) => {
                    let checkedList = [];
                    // 套餐需要取套餐子项目的选中
                    if (form.sourceFormType === SourceFormTypeEnum.COMPOSE) {
                        form.dispensingFormItems.forEach((item) => {
                            if (item.checkedList && item.checkedList.length) {
                                checkedList.push({
                                    id: item.id,
                                    composeChildren: item.checkedList,
                                });
                            }
                        });
                    } else {
                        checkedList = form.checkedList;
                    }

                    if (form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_CHINESE) {
                        return {
                            id: form.id,
                            sourceFormType: form.sourceFormType,
                            dispensingFormItems: checkedList,
                            doseCount: this.postData.dispensingForms.find((o) => o.id === form.id).doseCount ?? null,
                        };
                    }

                    return {
                        id: form.id,
                        sourceFormType: form.sourceFormType,
                        dispensingFormItems: checkedList,
                    };
                });
            },

            getDispensingItemInfo(formId, itemId) {
                let res = null;
                this.dispensingForms.forEach((form) => {
                    if (form.id === formId) {
                        res = form.dispensingFormItems.find((item) => item.id === itemId);
                    }
                });
                return res;
            },

            async submit() {
                if (this.buttonLoading) return false;

                this.buttonLoading = true;
                try {
                    const valueList = clone(this.valueList);
                    const postData = clone(this.postData);
                    // 处理postData; 需要合并valueList
                    const arr = flatArray(valueList);
                    postData.dispensingForms.forEach((o) => {
                        const isChineseForm = o.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_CHINESE;
                        o.dispensingFormItems.forEach((m) => {
                            if (m.composeChildren) {
                                m.composeChildren.forEach((n) => {
                                    const item = arr.find((p) => p.id === n.id);
                                    if (item) {
                                        const resItem = this.getDispensingItemInfo(o.id, m.id);
                                        const res = resItem?.composeChildren?.find((child) => child.id === n.id);
                                        Object.assign(n, {
                                            unitCount: item.unitCount,
                                            doseCount: item.doseCount,
                                            dispensingFormItemBatches: !isChineseForm ? item.dispensingFormItemBatches : undefined,
                                            shebaoDismountingFlag: res.shebaoDismountingFlag,
                                            traceableCodeRule: res.traceableCodeRule,
                                            traceableCodeList: res?.traceableCodeList ? res.traceableCodeList.map((code) => {
                                                const {
                                                    count, ...restCode
                                                } = code;
                                                return restCode;
                                            }) : [],
                                        });
                                    }
                                });
                            } else {
                                const item = arr.find((p) => p.id === m.id);
                                if (item) {
                                    const res = this.getDispensingItemInfo(o.id, item.id);
                                    Object.assign(m, {
                                        unitCount: item.unitCount,
                                        doseCount: item.doseCount,
                                        dispensingFormItemBatches: !isChineseForm ? item.dispensingFormItemBatches : undefined,
                                        shebaoDismountingFlag: res.shebaoDismountingFlag,
                                        traceableCodeRule: res.traceableCodeRule,
                                        traceableCodeList: res?.traceableCodeList ? res.traceableCodeList.map((code) => {
                                            const {
                                                count, ...restCode
                                            } = code;
                                            return restCode;
                                        }) : [],
                                    });
                                }
                            }
                        });
                    });

                    const { data } = await DispenseAPI.dispense(this._id, postData);
                    this.buttonLoading = false;

                    this.refreshQuickList(data, ['status', 'statusName']);
                    this.$store.commit('update_pharmacy_quick_list_summary', {
                        medicineCount: this.pharmacy.summary.medicineCount - 1,
                    });

                    this.$Toast({
                        message: '发药成功',
                        type: 'success',
                    });
                    this.fetchDetail();
                    this.$store.dispatch('getDispensingSummaryByDateRange', this.pharmacy.scrollParams);
                    localStorage.setObj('delivery_company_id', this._key, postData.deliveryCompanyId);
                } catch (err) {
                    console.error(err);
                    this.buttonLoading = false;

                    const {
                        code, message,
                    } = err;
                    if (code === 84115) {
                        const list = (err?.detail ?? []).filter((item) => (item.checkBatchList && item.checkBatchList.length > 0)).map((item) => ({
                            ...item,
                            dispensingFormItemBatches: this.findDispensingFormItemBatches(item),
                        }));
                        if (list && list.length > 0) {
                            await new PharmacyBatchesConfirmDialog({
                                list,
                                onConfirm: () => {
                                    this.confirmDelivery();
                                },
                                onClose: () => {
                                },
                            }).generateDialogAsync({ parent: this });
                        }
                    }

                    if (code === 470) {
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: message,
                        });
                    }
                    // 需要刷新发药配置与发药单
                    if ([17337, 17338, 17339].includes(code)) {
                        await this.$store.dispatch('fetchPharmacyConfig');
                        await this.fetchDetail();
                    }
                }
            },

            findDispensingFormItemBatches(findItem) {
                const formItems = [];
                this.dispensingForms.forEach((form) => {
                    form.dispensingFormItems.forEach((item) => {
                        item.composeChildren?.forEach((child) => {
                            if (child.keyId === item.id) {
                                formItems.push(...(item.dispensingFormItemBatches ?? []));
                            }
                        });
                        if (findItem.keyId === item.id) {
                            formItems.push(...(item.dispensingFormItemBatches ?? []));
                        }
                    });
                });
                return formItems;
            },

            createRefundTips(item, content, isCompose = false) {
                if (item.sourceItemType === OutpatientChargeTypeEnum.NO_CHARGE) return;
                const spec = this.getSpec(item.productInfo);
                const name = isCompose ? this.formatGoodsName(item) : item.name;
                content.push({
                    id: item.id,
                    name,
                    spec,
                    count: item.unitCount * (item.doseCount || 1),
                    unit: item.unit,
                },
                );
            },

            /**
             * @desc 确认退费但是需要二次确认
             * <AUTHOR>
             * @date 2018/07/28 20:01:25
             */
            refundConfirm(refundData) {
                const content = [];
                refundData.forEach((form) => {
                    // 规格显示调整
                    form.dispensingFormItems.forEach((item) => {
                        if (item.composeChildren && item.composeChildren.length) {
                            (item.composeChildren || []).forEach((child) => {
                                this.createRefundTips(child, content, true);
                            });
                        } else {
                            this.createRefundTips(item, content);
                        }
                    });
                });

                this.showRefundConfirm = true;
                this.currentRefundData = refundData;
                this.refundConfirmContent = content;
            },
            /**
             * @description: 二次确认退药提交
             * @date: 2024-12-09 14:58:22
             * @author: Horace
             * @return
            */
            handleRefundConfirm() {
                // 通过status判断药品的状态，筛选出已删除的药品
                const normalStatus = 1;
                const deletedItems = this.currentRefundData.reduce((res, form) => {
                    if (form.sourceFormType === SourceFormTypeEnum.COMPOSE) {
                        form?.dispensingFormItems.forEach((composeItem) => {
                            composeItem?.composeChildren.forEach((productItem) => {
                                if (productItem?.productInfo?.status !== normalStatus) {
                                    res.push(productItem);
                                }
                            });
                        });
                    } else {
                        form?.dispensingFormItems.forEach((item) => {
                            if (item?.productInfo?.status !== normalStatus) {
                                res.push(item);
                            }
                        });
                    }
                    return res;
                }, []);
                if (deletedItems && deletedItems.length) {
                    const deletedItemsName = deletedItems.map((item) => {
                        return item.name;
                    }).join('、');
                    const deletedItemsGoodsId = deletedItems.map((item) => {
                        return {
                            goodsId: item.productInfo.goodsId,
                            name: item.name,
                        };
                    });
                    this.$confirm({
                        type: 'warn',
                        title: '商品档案已删除',
                        confirmText: '确认退药',
                        content: `${deletedItemsName}档案已删除，退药后将恢复档案并退回库存，确认退药吗？`,
                        onConfirm: async () => {
                            // 恢复已删除的药品,接口调用会有不能恢复的情况
                            try {
                                await DispenseAPI.recoverGoodsArchive({
                                    items: deletedItemsGoodsId,
                                }, true);
                            } catch (e) {
                                if (e.code === 12818) {
                                    this.$alert({
                                        type: 'warn',
                                        title: e.message,
                                        content: e.detail,
                                    });
                                } else {
                                    this.$Toast({
                                        type: 'error',
                                        message: e.message,
                                    });
                                }
                                return;
                            }
                            this.unDispenseSubmit(this.currentRefundData);
                        },
                    });
                } else {
                    this.unDispenseSubmit(this.currentRefundData);
                }
            },

            /**
             * @desc
             * <AUTHOR>
             * @date 2018/07/28 19:46:57
             * @params
             * @return
             */
            async unDispenseSubmit(refundData) {
                if (this.buttonLoading) return false;
                this.buttonLoading = true;

                try {
                    const { data } = await DispenseAPI.undispense(this._id, {
                        dispensingForms: refundData,
                    });
                    this.buttonLoading = false;
                    this.showRefund = false;
                    this.$Toast({
                        message: '退药成功',
                        type: 'success',
                    });

                    // 退药后需要更新quicklist
                    this.pharmacy.quickList.forEach((item) => {
                        if (item.id === data.id) {
                            Object.assign(item, {
                                status: data.status,
                                statusName: data.statusName || '',
                            });
                        }
                    });
                    this.fetchDetail();
                } catch (err) {
                    console.error('unDispenseSubmit', err);

                    if ([17337, 17338, 17339].includes(err.code)) {
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: err.message,
                            onConfirm: async () => {
                                await this.$store.dispatch('fetchPharmacyConfig');
                                await this.fetchDetail();
                            },
                        });
                    } else {
                        if (!err.alerted) {
                            this.$Toast({
                                message: err.message,
                                type: 'error',
                            });
                        }
                    }
                    this.buttonLoading = false;
                }
            },

            async meanwhilePrintMedicineTag() {
                // const printData = {
                //     w: clone(this.tagForms), c: [],
                // };
                // this.selectPrintConfirm({
                //     selectedData: printData, isManualPreview: false,
                // });

                try {
                    const { dispenseId } = this;
                    if (!dispenseId) return;
                    const res = await DispenseAPI.fetchDetail(dispenseId, true);
                    const { data: sheet } = res;
                    const { dispensingSheet: data } = sheet;

                    const { dispensingForms } = data;

                    let forms = [];
                    const isSale = chargeIsRetail(data.sourceSheetType);
                    const tagTypes = isSale ? [ SourceFormTypeEnum.PRESCRIPTION_CHINESE] :
                        [ SourceFormTypeEnum.PRESCRIPTION_CHINESE,
                          SourceFormTypeEnum.PRESCRIPTION_WESTERN,
                          SourceFormTypeEnum.PRESCRIPTION_INFUSION ];
                    if (dispensingForms && dispensingForms.length) {
                        const newForms = clone(dispensingForms);
                        forms = newForms.filter((form) => {
                            if (tagTypes.includes(form.sourceFormType)) {
                                form.formItems = (form.dispensingFormItems || []).filter((item) => {
                                    if (item.status === dispenseItemStatusEnum.WAITING || item.status === dispenseItemStatusEnum.PART ||
                                        (item.status === dispenseItemStatusEnum.DISPENSED && !item.partDispense)) {
                                        item.checked = true;
                                        return item;
                                    }
                                    return false;
                                });
                                delete form.dispensingFormItems;
                                form.isFullChecked = true;
                                if (form.formItems.length > 0) {
                                    return form;
                                }
                                return false;
                            }
                            return false;
                        });
                    }

                    const {
                        doctorName, patientOrderNo, patient,
                    } = data;
                    const printOptions = getAbcPrintOptions('用药标签', {
                        forms,
                        patient,
                        doctorName,
                        patientOrderNo,
                        clinicName: this.currentClinic.clinicName,
                        isManualPreview: false,
                    });
                    AbcPrinter.abcPrint(printOptions);
                } catch (e) {
                    console.error('打印用药标签失败\n', e);
                }
            },

            selectPrintConfirm(params) {
                const {
                    selectedData, isManualPreview = true,
                } = params;
                console.log('selectedData', selectedData);
                this.printTags(selectedData, isManualPreview);
                this.showSelectPrint = false;
            },

            async printTags(selectedData, isManualPreview = true) {
                const {
                    doctorName, patientOrderNo,
                } = this.medicineTagPrintData;
                const patient = Clone(this.patient);
                const forms = selectedData.w.concat(selectedData.c);
                // 获取收费单对应的皮试结果
                try {
                    const infusionForms = forms.filter((form) => form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_INFUSION);
                    if (infusionForms && infusionForms.length) {
                        const astResultData = await PrintAPI.getAstResultByDispensing(this.dispenseId);
                        const astResultList = astResultData.data?.list || [];
                        infusionForms.forEach((infusionForm) => {
                            infusionForm.formItems.forEach((infusionFormItem) => {
                                const formItemAstData = astResultList.find((astResultItem) => astResultItem.formItemId === infusionFormItem.id);
                                if (formItemAstData) {
                                    infusionFormItem.ast = formItemAstData.ast || 0;
                                    infusionFormItem.astResult = formItemAstData.astResult;
                                }
                            });
                        });
                    }
                } catch (e) {
                    console.error('获取发药单皮试结果失败\n', e);
                }
                const printOptions = getAbcPrintOptions('用药标签', {
                    forms,
                    patient,
                    doctorName,
                    patientOrderNo,
                    clinicName: this.currentClinic.clinicName,
                    // 手动打印,不做自动打印筛选
                    isManualPreview,
                });
                await AbcPrinter.abcPrint(printOptions);
            },
            /**
             * @desc 获取打印二维码
             * <AUTHOR>
             * @date 2021-12-09 16:18:52
             */
            async fetchQrCode() {
                let qrcode = '';
                if (this.isOpenMp) {
                    try {
                        qrcode = await TpsAPI.genQrCode(this.patientOrderId);
                        return qrcode;
                    } catch (e) {
                        qrcode = '';
                    }
                }
                return qrcode;
            },
            async fetchPrintData(printType) {
                let printData = null;
                const printLoading = this.$Loading({
                    text: '准备打印...',
                    customClass: 'print-loading-wrapper',
                });
                try {
                    if (printType === this._printOptions.PRESCRIPTION.label && this.printable.prescription) {
                        const { data } = await PrintAPI.prescriptionPrintByPatientOrderIdForXuFang(this.patientOrderId, this.sourceSheetId);
                        printData = data;
                    }
                    if (printType === this._printOptions.INFUSION_EXECUTE.label && !this.disabledPrintInfusion) {
                        const { data } = await PrintAPI.chargeExecutedPrint(this.sourceSheetId, 1);
                        printData = data;
                    }

                    if (printType === this._printOptions.PATIENT_TAG.label && !this.disabledPrintPatientTag) {
                        const { data } = await PrintAPI.patientTagPrint(this.patientOrderId);
                        printData = data;
                    }
                    if (printType === this._printOptions.DISPENSING_ORDER.label && !this.disabledPrintMedicine) {
                        const { data } = await DispenseAPI.fetchDispensingPrint(this._id);
                        printData = {
                            ...data,
                            _dispensingConfig: {
                                isTakeMedicationTime: this.dispensingConfig.isTakeMedicationTime,
                            },
                        };
                    }
                    if (printType === this._printOptions.UNDISPENSING_ORDER.label && !this.disabledPrintUndispensing) {
                        const { data } = await PrintAPI.printDispensingsPrintUndispense(this._id);
                        printData = data;
                    }
                    if (printData) {
                        const qrCode = await this.fetchQrCode();
                        printData.qrCode = qrCode;
                        if (printData.printData) {
                            printData.printData.qrCode = qrCode;
                            printData.printData._dispensingConfig = {
                                isTakeMedicationTime: this.dispensingConfig.isTakeMedicationTime,
                            };
                        }
                        return printData;
                    }
                    return null;
                } catch (e) {
                    console.error(e);
                } finally {
                    printLoading.close();
                }
            },
            /**
             * @Description: 处理处方分类逻辑
             * <AUTHOR> Cai
             * @date 2022/07/05 11:08:35
             */
            async printPrecriptionHandler() {
                const data = await this.fetchPrintData(this._printOptions.PRESCRIPTION.label) || {};
                if (data.prescriptionPrintType === PrecriptionPrintType.NOMAL_PR || data.prescriptionPrintType === PrecriptionPrintType.HISTORY_PR) {
                    const { printData } = data;
                    const { isDispensedByNameInWaiting } = this.printMedicalDocumentsConfig.prescription;
                    if (this.status === dispenseStatusEnum.WAITING && isDispensedByNameInWaiting) {
                        //如果未发药 此时还没有发药人 核发签名应该是页面选择的发药人
                        const findItem = this.employeeAll.find((_item) => _item.employeeId === this.postData.dispensedByIds[0]);
                        if (findItem) {
                            printData.dispensedByNameInWaiting = findItem.employeeName ?? '';
                            printData.dispensedByHandSignInWaiting = findItem.handSign ?? '';
                        }
                    }
                    await AbcPrinter.abcPrint({
                        templateKey: window.AbcPackages.AbcTemplates?.prescription,
                        printConfigKey: ABCPrintConfigKeyMap.prescription,
                        data: printData,
                        onPrintSuccess: async () => {
                            // 打印成功的回调
                            if (this.status === dispenseStatusEnum.WAITING && isDispensedByNameInWaiting) {
                                const postData = {
                                    dispensedByIds: this.postData.dispensedByIds,
                                };
                                //上报发药打印日志
                                if (this.dispenseId) {
                                    await PrintAPI.printDispensingLog(this.dispenseId,postData);
                                }
                            }
                        },
                    });
                } else if (data.prescriptionPrintType === PrecriptionPrintType.IMG_PR) {
                    const { attachments: printImgList = [] } = data.printData;
                    await AbcPrinter.abcPrint({
                        templateKey: window.AbcPackages.AbcTemplates.medicalFeeSocial,
                        printConfigKey: ABCPrintConfigKeyMap.social,
                        data: {},
                        extra: {
                            getHTML: () => {
                                let html = '';
                                for (const img of printImgList) {
                                    html += this.imgDirectionHandle(img.url);
                                }
                                return `<div>${html}</div>`;
                            },
                        },
                    });
                }
            },
            /**
             * @Description: 根据图片不同方向渲染
             * <AUTHOR> Cai
             * @date 2022/07/05 15:57:56
             */
            imgDirectionHandle(img) {
                return `
                        <div>
                             <div style="height: 600px;"></div>
                             <div style="position: absolute;top:0;width: 100%; height: 100%; margin: 0 auto;
                             text-align: center;padding: 5mm;box-sizing: border-box;">
                                <img src="${img}" style="max-width: 100%; max-height: 100%;" alt="">
                            </div>
                        </div>
                        `;
            },
            async printHandler(selected) {
                selected = selected.filter((printItem) => {
                    let printAble = false;
                    this.printOptions.forEach((optionItem) => {
                        if (optionItem.value === printItem && !optionItem.disabled) {
                            printAble = true;
                        }
                    });
                    return printAble;
                });
                if (selected.includes(this._printOptions.INFUSION_EXECUTE.label) ||
                    selected.includes(this._printOptions.PATIENT_TAG.label) ||
                    selected.includes(this._printOptions.DISPENSING_ORDER.label) ||
                    selected.includes(this._printOptions.UNDISPENSING_ORDER.label)) {
                    await AbcPrinter.abcPrint(async () => {
                        const printPropsList = [];
                        for (let i = 0; i < selected.length; i++) {
                            const select = selected[i];
                            const printData = await this.fetchPrintData(select);
                            const printTaskOptions = getAbcPrintOptions(select, printData);
                            if (printTaskOptions) {
                                printPropsList.push(printTaskOptions);
                            }
                        }
                        return printPropsList;
                    });
                } else if (selected.includes('患者标签')) {
                    await this.printPatientTag();
                }
                if (selected.includes(this._printOptions.PRESCRIPTION.label)) {
                    await this.printPrecriptionHandler();
                }
                if (selected.includes('用药标签')) {
                    this.printMedicineTagHandler();
                    // this.showSelectPrint = true;
                }
                if (selected.includes(this._printOptions.DECOCTION_CRAFT_CARD.label)) {
                    await this.printDecoctionCraftCard();
                }
            },
            formatPrintData(data) {
                const commonInfo = {
                    patient: data.patient,
                    auditName: data.auditName,
                    organ: data.organ,
                    chargedByName: data.chargedByName,
                    chargedTime: data.chargedTime,
                    compoundName: data.compoundName,
                    sellerName: data.sellerName,
                    dispensedByName: data.dispensedByName,
                    dispensedTime: data.dispensedTime,
                    isDecoction: data.isDecoction,
                    contactMobile: data.contactMobile,
                    doctorName: data.doctorName,
                    patientOrderNo: data.patientOrderNo,
                    deliveryInfo: data.deliveryInfo,
                    netIncomeFee: data.netIncomeFee,
                    departmentName: data.departmentName,
                    diagnose: data.diagnose,
                    doctorAdvice: data.doctorAdvice,
                };
                const chineseForms = clone(data.dispensingForms.filter((form) => {
                    return form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_CHINESE;
                }));
                const forms = [];
                const pageSize = chineseForms.length; // 根据中药处方拆分成几个不同的发药单
                if (pageSize) {
                    for (let i = 0; i < pageSize; i++) {
                        const dispensingForms = [];
                        if (i === 0) {
                            // 第一个发药单需要将西药和材料等信息加入进来，加入第一个中药处方，循环是为了保持之间的相互关系
                            data.dispensingForms.forEach((form) => {
                                if (form.sourceFormType !== SourceFormTypeEnum.PRESCRIPTION_CHINESE) {
                                    dispensingForms.push(form);
                                }
                            });
                            dispensingForms.push(chineseForms[i]);
                        } else {
                            dispensingForms.push(chineseForms[i]); // 直接将分出的中药处方push 进来
                        }
                        forms.push({
                            ...commonInfo,
                            dispensingForms,
                        });
                    }
                } else {
                    forms.push(data);
                }
                return forms;
            },

            /**
             * @desc 更新发药单的打印状态
             * <AUTHOR>
             * @date 2021-07-29 14:28:18
             * @params
             * @return
             */
            async updateTaskPrinted(dispensingId) {
                try {
                    const postData = {
                        printedNumbers: [{
                            'dispensingId': dispensingId,
                            'isPrinted': 1,
                        }],
                    };
                    const { data } = await DispensaryAPI.updateDispensingPrinted(postData);
                    if (data.dispensingSheetAbstracts) {
                        const updateData = {
                            id: dispensingId,
                            params: {
                                printedNumber: data.dispensingSheetAbstracts[0].printedNumber || 0,
                            },
                        };
                        this.$store.dispatch('updatePharmacyQuickItem', updateData);
                    }
                } catch (e) {
                    console.warn('更新打印状态失败', e);
                }
            },
            /**
             * @desc 打印患者标签
             * <AUTHOR>
             * @date 2020/2/18
             */
            async printPatientTag() {
                const { data } = await PrintAPI.patientTagPrint(this.patientOrderId);
                const printOptions = getAbcPrintOptions('患者标签', data);
                AbcPrinter.abcPrint(printOptions);
            },
            /**
             * @description: 打印煎药工艺卡
             * @date: 2024-09-19 10:39:02
             * @author: Horace
             * @return
            */
            async printDecoctionCraftCard() {
                const { data } = await DispenseAPI.fetchDispensingPrint(this._id);
                const { logo } = this.clinicBasicConfig;
                const printOptions = getAbcPrintOptions('煎药工艺卡',{
                    ...data,
                    title: '济华中医馆煎药工艺卡',
                    dispensingForms: data.dispensingForms?.filter((form) => form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_CHINESE),
                    logo: data.organ?.logo || logo,
                });

                AbcPrinter.abcPrint({
                    ...printOptions,
                    mode: PrintMode.Electron,
                });
            },

            /**
             * @desc 关闭发药单
             * <AUTHOR> Yang
             * @date 2020-10-19 10:19:49
             */
            closeHandler() {
                this.$confirm({
                    type: 'warn',
                    title: '关闭确认',
                    customClass: 'flex',
                    content: ['关闭后发药单状态将从【待发】转为【关闭】','已关闭的发药单可重新打开发药'],
                    onConfirm: () => {
                        this.closeSubmit();
                    },
                });
            },
            async closeSubmit() {
                const { data } = await DispenseAPI.closeDispensing(this._id);
                this.refreshQuickList(data, ['status', 'statusName']);

                this.status = data.status;
                this.$Toast({
                    message: '关闭成功',
                    type: 'success',
                });
                this.$store.dispatch('getDispensingSummaryByDateRange', this.pharmacy.scrollParams);

                // 更新 records
                this.fetchDetail();
            },

            reopenHandler() {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '该发药单已关闭。是否确认打开发药单发药？',
                    onConfirm: () => {
                        this.reopenSubmit();
                    },
                });
            },
            async reopenSubmit() {
                const { data } = await DispenseAPI.reopenDispensing(this._id);
                this.refreshQuickList(data, ['status', 'statusName']);
                this.status = data.status;
                this.$Toast({
                    message: '重新打开成功',
                    type: 'success',
                });
                this.$store.dispatch('getDispensingSummaryByDateRange', this.pharmacy.scrollParams);

                // 更新 records
                this.fetchDetail();
            },

            refreshQuickList(data, key) {
                this.pharmacy.quickList.forEach((it) => {
                    const id = data.id || data.dispensingSheetId;

                    if (it.id === id) {
                        if (typeof key === 'string') {
                            it[key] = data[key];
                        } else {
                            key.forEach((o) => {
                                it[o] = data[o];
                            });
                        }
                    }
                });
            },
            async asyncCollectionTraceCodeDialog() {

                return new Promise((resolve, reject) => {
                    this._collectionTraceCodeDialog = new CollectionTraceCodeDialog({
                        formItems: this.traceCodeDispenseItems,
                        dispensedFormItems: this.traceCodeDispensedItems,
                        filterItemStatus: this.traceCodeFilterItemStatus,
                        needInitValidate: false, // 这个方法调用之前执行过TraceCode.validate了
                        patientOrderId: this.patientOrderId,
                        sceneType: SceneTypeEnum.PHARMACY,
                        disabled: this.isDisabledCollection,
                        isSelectedSocialPay: this.isShebaoPay,
                        isDisabledActualCount: true,
                        isEnableTraceTempSave: this.isEnableTraceTempSave,
                        onConfirm: (_,action) => {
                            this.saveTraceCodeHandler();
                            if (action !== 'tempSave') {
                                resolve();
                            } else {
                                reject();
                            }
                        },
                        onClose: () => {
                            reject();
                        },
                    });
                    this._collectionTraceCodeDialog.generateDialogAsync();
                });
            },

            // 检查是否存在今天带西药的部分发药的
            checkIsExistTodayPartialDispense() {
                let isPartialDispense = false;
                // 待发药列表中是否存在今天的部分发药的单子
                let isExistTodayPartialDispenseItem = false;

                const filterChineseMedicine = (dispenseItem) => {
                    return dispenseItem.productType === GoodsTypeEnum.MEDICINE && [
                        GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].WesternMedicine,
                        GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].CPM,
                    ].includes(dispenseItem.productSubType);
                };

                // 待发列表
                const waitDispenseList = this.dispensingForms.reduce((res, form) => {
                    const _waitDispenseList = form.dispensingFormItems.filter((dispensingFormItem) => (
                        [dispenseItemStatusEnum.WAITING, dispenseItemStatusEnum.PART].includes(dispensingFormItem.status)
                    ));
                    return [...res , ..._waitDispenseList];
                }, []).filter(filterChineseMedicine);

                // 没有待发的西药
                if (!waitDispenseList.length) return false;

                const allDispenseItems = this.dispensingForms.reduce((res, form) => {
                    return [...res, ...(form.dispensingFormItems || [])];
                }, []);

                // 已选发药项
                const selectedDispenseList = this.dispensingForms.reduce((res, form) => {
                    return [...res, ...(form.checkedList || [])];
                }, [])
                    .map((item) => allDispenseItems.find((d) => d.id === item.id))
                    .filter(filterChineseMedicine);

                if (!selectedDispenseList.length) return false;

                if (selectedDispenseList.length !== waitDispenseList.length) {
                    // 代发药品未全部勾选，视为部分发药
                    isPartialDispense = true;
                } else {
                    // 编辑发药列表
                    const editDispenseList = this.dispensingForms.reduce((res, form) => {
                        return [...res , ...(form.valueList || [])];
                    }, []);

                    // 判断已选药品是否全部发出
                    isPartialDispense = waitDispenseList.some((item) => {
                        const willDispenseCount = editDispenseList.find((editItem) => editItem.id === item.id)?.unitCount;
                        return willDispenseCount !== item.totalCount;
                    });
                }

                if (isPartialDispense) {
                    // 是否存在今天部分发药的药品
                    isExistTodayPartialDispenseItem = waitDispenseList.some((dispensingFormItem) => {
                        const today = new Date();
                        today.setHours(0,0,0,0);
                        const dispenseDate = new Date(dispensingFormItem.created);
                        return dispenseDate.getTime() < Date.now() &&
                            dispenseDate.getTime() > today.getTime();
                    });
                }

                return this.status === dispenseStatusEnum.WAITING && isExistTodayPartialDispenseItem;
            },

            confirmDelivery() {
                if (this.buttonLoading) return false;

                this.$refs.dispenseForm.validate((valid) => {
                    if (valid) {
                        const continueCheck = async () => {
                            if (this.isWholeBillCharge) {
                                const {
                                    disabledItems = [],
                                    shortageItems = [],
                                } = getWarnChargeItemGroup(this.dispensingForms, 'dispensingFormItems');
                                console.log(disabledItems, shortageItems);
                                if ((disabledItems.length > 0 || shortageItems.length > 0)) {
                                    this.$alert({
                                        type: 'warn',
                                        title: '提示',
                                        content: '发药单内包含停售/库存不足商品，请医生调整门诊并收费后再发药',
                                    });
                                    return false;
                                }
                            }
                            if (this.traceCodeCollectionCheck === CollectionTraceCodeCheck.dispensing && this.traceCodeDispenseItems?.length && !this.isDisabledCollection) {
                                const res = await TraceCode.validate({
                                    scene: TraceCodeScenesEnum.PHARMACY,
                                    sceneType: SceneTypeEnum.PHARMACY,
                                    dataList: TraceCode.getFlatItems(this.traceCodeDispenseItems),
                                    getUnitInfo: (item) => {
                                        return {
                                            ...item,
                                            unitCount: TraceCode.getUnitCount(item),
                                        };
                                    },
                                    needGetMaxTraceCountList: TraceCode.isSupportTraceCodeForceCheckPharmacy(),
                                    patientOrderId: this.patientOrderId,
                                });
                                if (!res.flag) {
                                    // 一个追溯码都没，需要提醒
                                    await this.asyncCollectionTraceCodeDialog();
                                }
                            }
                            this.showDialog = true;
                        };

                        if (this.checkIsExistTodayPartialDispense() && this.isShebaoPay) {
                            this.$confirm({
                                type: 'warn',
                                title: '依码支付风险提示',
                                content: '医保结算的药品必须在当日完成发药并上报追溯码。建议本单药品当日全部发出，否则将影响追溯码采集率和医保拨付！',
                                confirmText: '返回修改',
                                cancelText: '继续发药',
                                size: 'medium',
                                onCancel: () => {
                                    continueCheck();
                                },
                            });
                        } else {
                            continueCheck();
                        }
                    }
                });
            },
            async handleCallDispensary() {
                if (this.callLoading) return;

                this.callLoading = true;
                try {
                    await PharmacyCallingAPI.dispensingQuickCall(this.dispensingCallingItemId, {
                        pharmacyNo: this.curPharmacy,
                    });
                    this.$Toast({
                        message: '已呼叫取药',
                        type: 'success',
                    });
                } catch (e) {
                    this.$Toast({
                        message: e.message || '呼叫取药失败',
                        type: 'error',
                    });
                } finally {
                    this.callLoading = false;
                }
            },

            handleOpenTraceCodeDialog() {
                this._collectionTraceCodeDialog = new CollectionTraceCodeDialog({
                    formItems: this.traceCodeDispenseItems,
                    dispensedFormItems: this.traceCodeDispensedItems,
                    filterItemStatus: this.traceCodeFilterItemStatus,
                    isDisabledActualCount: true,
                    disabled: this.isDisabledCollection,
                    patientOrderId: this.patientOrderId,
                    sceneType: SceneTypeEnum.PHARMACY,
                    isSelectedSocialPay: !!this.isShebaoPay,
                    canReReportTraceCode: this.canReReportTraceCode,
                    isEnableTraceTempSave: this.isEnableTraceTempSave,
                    onConfirm: (_,action) => this.saveTraceCodeHandler(action),
                    onClose: () => {
                        this._collectionTraceCodeDialog = null;
                    },
                });
                this._collectionTraceCodeDialog.generateDialogAsync();
            },
            async saveTraceCodeHandler(action) {
                const isReReport = action === 'reReport' && this.canReReportTraceCode;
                if (this.status >= dispenseStatusEnum.DISPENSED && !isReReport) return;
                try {
                    await DispensaryAPI.saveTraceCode(this.dispenseId, {
                        isReReport: Number(isReReport),
                        list: (isReReport ? this.traceCodeDispensedItems : this.traceCodeDispenseItems).filter((item) => (isReReport ? item.status === dispenseItemStatusEnum.DISPENSED : item.status !== dispenseItemStatusEnum.DISPENSED && item.status !== dispenseItemStatusEnum.RETURN)).map((item) => {
                            return {
                                id: item.id,
                                shebaoDismountingFlag: item.shebaoDismountingFlag,
                                traceableCodeRule: item.traceableCodeRule,
                                traceableCodeList: item.traceableCodeList ? item.traceableCodeList.map((code) => {
                                    const {
                                        count, ...restCode
                                    } = code;
                                    return restCode;
                                }) : [],
                                composeChildren: item.composeChildren && item.composeChildren.filter((it) => it.status !== dispenseItemStatusEnum.DISPENSED && it.status !== dispenseItemStatusEnum.RETURN).map((it) => {
                                    return {
                                        id: it.id,
                                        shebaoDismountingFlag: it.shebaoDismountingFlag,
                                        traceableCodeRule: item.traceableCodeRule,
                                        traceableCodeList: it.traceableCodeList ? it.traceableCodeList.map((code) => {
                                            const {
                                                count, ...restCode
                                            } = code;
                                            return restCode;
                                        }) : [],
                                    };
                                }),
                            };
                        }),
                    });
                    this.$Toast({
                        type: 'success',
                        message: '保存成功',
                    });
                } catch (e) {
                    console.error(e);
                }
            },

            async getLockInfo() {
                try {
                    this.lockedInfo = null;
                    const data = await PatientOrderLockService.getLockInfo({
                        patientOrderId: this.patientOrderId,
                        businessKeys: [LockBusinessKeyEnum.CHARGE],
                    });
                    const {
                        result, identity, value,
                    } = data || {};
                    const { businessScene } = value || {};
                    if (businessScene !== ChargeBusinessSceneEnum.CHARGE_SHEET_REFUND) return;
                    if (result === 1 && identity) {
                        this.lockedInfo = data;
                    }
                } catch (err) {
                    Logger.error({
                        scene: 'getLockInfo treatment',
                        err,
                    });
                }
            },
            onLockSocket(socketData, type) {
                const {
                    key, businessKey, value,
                } = socketData || {};
                const { businessScene } = value || {};
                if (businessKey !== LockBusinessKeyEnum.CHARGE) return;
                if (businessScene !== ChargeBusinessSceneEnum.CHARGE_SHEET_REFUND) return;
                if (key === this.patientOrderId) {
                    if (type === 'lock') {
                        this.lockedInfo = socketData;
                    } else {
                        this.lockedInfo = null;
                        this.fetchDetail();
                    }
                }
            },
            // 判断当前日期是否是当月
            isSameYearMonth(inputDate) {
                if (!inputDate) {
                    return false;
                }
                try {
                    // 将输入转换为Date对象
                    const date = new Date(inputDate);

                    // 获取当前日期
                    const now = new Date();

                    // 比较年份和月份
                    return now.getFullYear() === date.getFullYear() &&
                        now.getMonth() === date.getMonth();
                } catch (error) {
                    return false;
                }
            },
            handleRefund() {
                // 医保结算单，跨月，并且是限定地区的定点机构
                if (this.disabledRefundRegion && !this.isSameYearMonth(this.dispensedTime) && !!this.shebaoCardInfo) {
                    this.$confirm({
                        type: 'warn',
                        title: '医保风险提示',
                        content: () => {
                            return (
                                <abc-flex vertical>
                                    <abc-text>医保中心不支持跨月退药上报。</abc-text>
                                    <abc-text>如继续退药，追溯码将无法上报，药品再次销售时医保将判定为重复销售。是否继续？</abc-text>
                                </abc-flex>
                            );
                        },
                        onCancel: () => {
                            this.showRefund = true;
                        },
                        cancelText: '仍要退药',
                        confirmText: '取消',
                    });
                    return;
                }
                this.showRefund = true;
            },
        },
    };
</script>

<style lang="scss" scoped>
.pharmacy-name-tag-wrapper {
    padding: 4px 6px;
    margin-left: 12px;
    line-height: 16px;
    color: #7a8794;
    border: 1px solid #e6eaee;
    border-radius: var(--abc-border-radius-small);
}

.custom-record-tab {
    margin-left: -4px;
}

.pharmacy-other-form-content-wrapper {
    display: flex;
    padding: 12px;
    margin: 16px 0;
    font-size: 12px;
    line-height: 16px;
    background-color: #ffffff;
    border: 1px solid $P1;
    border-radius: var(--abc-border-radius-small);

    .pharmacy-other-form-content-title {
        margin-right: 24px;
        font-weight: 500;
    }

    .pharmacy-other-form-content-item-wrapper {
        flex: 1;

        > div {
            display: flex;
            align-items: center;

            > span {
                margin-left: 4px;
                color: #aab4bf;
            }
        }

        > div + div {
            margin-top: 8px;
        }
    }
}

.other-information {
    margin-top: 16px;
    color: $T2;
}
</style>

<style lang="scss">
.pharmacy-operation-popover_wrapper {
    width: 510px;
    max-height: 500px;
    overflow-y: auto;

    .pharmacy-operation-title {
        margin-bottom: 6px;
        font-weight: 500;
        line-height: 20px;
    }

    .pharmacy-operation-content {
        display: flex;
        flex-direction: column;
        font-size: 12px;

        &.is-chinese {
            flex-direction: row;
            flex-wrap: wrap;

            .pharmacy-operation-item {
                display: flex;
                justify-content: center;
                width: 25%;
                margin-bottom: 6px;

                &:nth-child(4n + 1) {
                    justify-content: flex-start;
                }

                &:nth-child(4n) {
                    justify-content: flex-end;
                }

                .pharmacy-operation-item-name {
                    max-width: 85px;
                    margin-right: 6px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }
            }
        }

        .pharmacy-operation-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 6px;
        }
    }

    .pharmacy-operation-custom-item {
        margin-bottom: 6px;
        font-size: 12px;
        color: $T3;
    }
}

.dispense-employee-form-item-group {
    .abc-input__inner {
        height: calc(var(--abc-input-height) - 2px) !important;
    }
}
</style>
