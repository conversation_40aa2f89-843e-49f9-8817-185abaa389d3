import Abc<PERSON>rin<PERSON> from '@/printer';
import { mapGetters } from 'vuex';
import PreAbcPrint from '@/printer/index-v2/pre-print-handler';
import { TicketCharacterCountMap } from '@/printer/constants';

export default {
    computed: {
        ...mapGetters([
            'currentClinic',
            'clinicBasicConfig',
            'dispensingConfig',
        ]),
    },
    watch: {
        postData: {
            handler(newPostData) {
                this.$nextTick(() => {
                    if (this._dispense) {
                        this.setCurrentExampleData();
                        this.printInstance = null;
                    }
                    if (this._cashier) {
                        this.printInstance = null;
                    }
                    this.updatePreview(newPostData);
                });

            },
            deep: true,
        },
    },
    created () {
        AbcPrinter.setGlobalConfig();
        PreAbcPrint.setGlobalConfig();
    },
    beforeDestroy() {
        this.destroyPrintInstanceStyle();
        this.$destroyed = true;
    },
    methods: {
        destroyPrintInstanceStyle() {
            this._destroyStyle?.();
            this._destroyStyle = null;
        },
        async updatePreview(newPostData) {
            await this.mountPrintInstance();
            // 更新预览界面
            await this.updateInstanceGlobalConfig(newPostData);
        },
        async mountPrintInstance() {
            if (this.printInstance) return;

            // 处理示例数据
            if (this.handleTransExampleData) {
                this.handleTransExampleData();
            }
            if (this.currentExampleData && !this.handleTransExampleData) {
                if (!this.currentExampleData.shebaoCardInfo) {
                    this.currentExampleData.shebaoCardInfo = {};
                }
                if (window.$abcSocialSecurity.config.isGuangdongShenzhen) {
                    this.currentExampleData.isGuangdongShenzhen = true;
                }

                Object.assign(this.currentExampleData, {
                    doctorName: '胡青牛',
                    qrCode: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFgAAABYCAYAAABxlTA0AAAAAklEQVR4AewaftIAAASfSURBVO3BUY7kypEAQXei7n9lXyWgAB4SHKpaPbn6oZlA/CUVQ2WpGCq7iqEyKhaVOxW/obKrGCp/w8XrqIvXUR/+oeKnVO5UfEPljspSMVR2KncqFpVRMSoWlScV/w2V5eJ11MXrqA9/oPKk4m+rGCpPVJ6o7FRGxU+pPKnYXbyO+nCIyjcq7lR8o2KojIpF5X/p4nXUxeuoD4dVDJVRsVMZFU8qfqpiqCwVJ128jrp4HfXhDyr+toqhslSMip3KqNipjIqhslMZFT9V8VMXr6M+/IPKSSqjYlEZFUNlqRgqo+IbFUNlpzIqdiq/cfE66uJ1lP0LB6g8qXiislTcUfmNiv8PF6+jLl5HfVRGxROVUbGofKvip1RGxZOKofINld+ouKOyXLyO+lTcUVkq7qgsFUNlVCwqd1SWijsVO5W/RWWpeKIyKobKonKnYrl4HXXxOuqjMipGxZOKReWOylIxVEbFTuVbFYvKk4o7KovKnYql4knFUNldvI66eB0lEP9WMVSWijsqS8VQeVIxVJaKoTIqFpVR8S2VpWKo7CruqCwV/w2V5eJ11If/QOWJyqjYqdypWFS+pTIqnlR8Q2VU7FSeVAyV3cXrqIvXUfYv3FDZVTxR+amKOypLxR2Vb1TcUVkqhsqoWFRGxVDZVewuXkddvI768AcVO5VRsaiMiqGyqxgqi8qoGBWLyqgYFYvKqBgqi8pJFUNlVCwXr6ME4kbFojIqhspSMVRGxaLypOKOyk9V7FRGxVBZKobKrmKojIpvXLyOungd9akYKk9URsWiMiqGyq5iqOxURsUTlZ3KE5WdyqgYKt9QuVOxXLyOungd9eE/qBgqu4qhMip2KqPiicqTip9S2VUMlVGxqxgqT1SWi9dRH5VRMVR2FUNlVzFUdhU7lTsVi8qoGCpLxR2Vn6oYKkvFk4qhMiqWi9dRF6+jPhVD5acqhspJKjuVb1UMlZ3KqHhSsasYKsvF66iL11EflVExVL6hMiqGylIxVEbFUvGtip3KqBgqS8W3KobKUnFHZal4cvE6SiB+oWKo7CqGyrcqdiqjYqcyKnYqu4o7KruKofKkYrl4HXXxOupT8S2Vncqo2KmMiqGyqxgqS8UdlaXiicq3VEbFTmVU7FR2F6+jLl5HffgDlScVP6UyKhaVb1UMlScqu4qh8g2V37h4HfVRuVOxqDxRuVPxRGWpGCqjYlF5ojIqhso3VO6ofENlVOwuXkddvI76VDypeFJxR2WpuKOyqNxR+UbFnYpF5VsVT1S+pbJcvI66eB0lEH9JxVD5qYqdyqjYqYyKobJUPFEZFUNlqRgqu4qhMiqWi9dRH/6h4qdU7lTsVH5DZVQsFUNlVOxURsWTip+q2F28jrp4HfXhD1SeVDxRWSpGxROV/yWV31AZFcvF66iL11EfDqlYVH6j4onKHZVdxVDZVQyVpWKojIpF5cnF66gPh1UMlScVQ2VRGRW7ijsqS8VvqHyrYnfxOuriddSHP6j4G1RGxROVJyq7ijsVi8rfUjFUloonF6+jLl5HffgHlb9FZacyKp5U7FR2KidVDJVvqSwXr6P+DwJnxhBDG+d2AAAAAElFTkSuQmC',
                    invoiceQrcode: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFgAAABYCAYAAABxlTA0AAAAAklEQVR4AewaftIAAASfSURBVO3BUY7kypEAQXei7n9lXyWgAB4SHKpaPbn6oZlA/CUVQ2WpGCq7iqEyKhaVOxW/obKrGCp/w8XrqIvXUR/+oeKnVO5UfEPljspSMVR2KncqFpVRMSoWlScV/w2V5eJ11MXrqA9/oPKk4m+rGCpPVJ6o7FRGxU+pPKnYXbyO+nCIyjcq7lR8o2KojIpF5X/p4nXUxeuoD4dVDJVRsVMZFU8qfqpiqCwVJ128jrp4HfXhDyr+toqhslSMip3KqNipjIqhslMZFT9V8VMXr6M+/IPKSSqjYlEZFUNlqRgqo+IbFUNlpzIqdiq/cfE66uJ1lP0LB6g8qXiislTcUfmNiv8PF6+jLl5HfVRGxROVUbGofKvip1RGxZOKofINld+ouKOyXLyO+lTcUVkq7qgsFUNlVCwqd1SWijsVO5W/RWWpeKIyKobKonKnYrl4HXXxOuqjMipGxZOKReWOylIxVEbFTuVbFYvKk4o7KovKnYql4knFUNldvI66eB0lEP9WMVSWijsqS8VQeVIxVJaKoTIqFpVR8S2VpWKo7CruqCwV/w2V5eJ11If/QOWJyqjYqdypWFS+pTIqnlR8Q2VU7FSeVAyV3cXrqIvXUfYv3FDZVTxR+amKOypLxR2Vb1TcUVkqhsqoWFRGxVDZVewuXkddvI768AcVO5VRsaiMiqGyqxgqi8qoGBWLyqgYFYvKqBgqi8pJFUNlVCwXr6ME4kbFojIqhspSMVRGxaLypOKOyk9V7FRGxVBZKobKrmKojIpvXLyOungd9akYKk9URsWiMiqGyq5iqOxURsUTlZ3KE5WdyqgYKt9QuVOxXLyOungd9eE/qBgqu4qhMip2KqPiicqTip9S2VUMlVGxqxgqT1SWi9dRH5VRMVR2FUNlVzFUdhU7lTsVi8qoGCpLxR2Vn6oYKkvFk4qhMiqWi9dRF6+jPhVD5acqhspJKjuVb1UMlZ3KqHhSsasYKsvF66iL11EflVExVL6hMiqGylIxVEbFUvGtip3KqBgqS8W3KobKUnFHZal4cvE6SiB+oWKo7CqGyrcqdiqjYqcyKnYqu4o7KruKofKkYrl4HXXxOupT8S2Vncqo2KmMiqGyqxgqS8UdlaXiicq3VEbFTmVU7FR2F6+jLl5HffgDlScVP6UyKhaVb1UMlScqu4qh8g2V37h4HfVRuVOxqDxRuVPxRGWpGCqjYlF5ojIqhso3VO6ofENlVOwuXkddvI76VDypeFJxR2WpuKOyqNxR+UbFnYpF5VsVT1S+pbJcvI66eB0lEH9JxVD5qYqdyqjYqYyKobJUPFEZFUNlqRgqu4qhMiqWi9dRH/6h4qdU7lTsVH5DZVQsFUNlVOxURsWTip+q2F28jrp4HfXhD1SeVDxRWSpGxROV/yWV31AZFcvF66iL11EfDqlYVH6j4onKHZVdxVDZVQyVpWKojIpF5cnF66gPh1UMlScVQ2VRGRW7ijsqS8VvqHyrYnfxOuriddSHP6j4G1RGxROVJyq7ijsVi8rfUjFUloonF6+jLl5HffgHlb9FZacyKp5U7FR2KidVDJVvqSwXr6P+DwJnxhBDG+d2AAAAAElFTkSuQmC',
                    healthCardPayLevel: '市医保(普通)',
                    shebaoCardInfo: {
                        feeType: '职工',
                    },
                    healthCardNo: '*********',
                    departmentName: '内科',
                    doctorAdvice: this.isPrescriptionV2Config ? '饮食规律宜清淡，忌烟酒，忌辛辣荤腥' : '1.多喝水，保持身体充足水分<br/>2.饮食规律宜清淡，忌烟酒，忌辛辣荤腥',
                });

                Object.assign(this.currentExampleData.shebaoCardInfo, {
                    personalCode: 'TK1203163',
                    extend: {
                        personalCode: 'TK1203163',
                    },
                });

                if (this.currentExampleData.patient) {
                    Object.assign(this.currentExampleData.patient, {
                        idCard: '50000000000000000X',
                        weight: '45',
                        address: {
                            addressProvinceName: '四川省',
                            addressCityName: '成都市',
                            addressDistrictName: '高新区',
                            addressDetail: '交子大道',
                        },
                    });
                }

                if (this.currentExampleData.organ || this.currentExampleData.organPrintView) {
                    const {
                        name,
                        shortName,
                        hisType,
                    } = this.currentClinic;
                    const { logo } = this.clinicBasicConfig;
                    this.currentExampleData.organ && Object.assign(this.currentExampleData.organ, {
                        name,
                        shortName,
                        logo,
                        hisType,
                    });
                    this.currentExampleData.organPrintView && Object.assign(this.currentExampleData.organPrintView, {
                        name,
                        shortName,
                        logo,
                        hisType,
                    });
                }

                if (this.currentExampleData.medicalRecord) {
                    Object.assign(this.currentExampleData.medicalRecord, {
                        doctorAdvice: '1.多喝水，保持身体充足水分<br/>2.饮食规律宜清淡，忌烟酒，忌辛辣荤腥',
                    });
                }
            }
            const pageInfo = this.previewPage || {
                size: 'A5',
                orientation: 1,
            };
            if (this.dispensingConfig.isTakeMedicationTime) {
                this.currentExampleData._dispensingConfig = {
                    isTakeMedicationTime: this.dispensingConfig.isTakeMedicationTime,
                };
            }
            const printInstance = new window.AbcPackages.AbcPrint({
                template: this.getCurrentTemplate(),
                page: pageInfo,
                originData: this.currentExampleData,
                extra: {
                    isPreview: true,
                    TicketCharacterCountMap,
                },
            });
            await printInstance.init();
            this.destroyPrintInstanceStyle();
            if (!this.$destroyed) {
                this._destroyStyle = printInstance.loadInstanceStyle();
            }
            if (this.$refs.previewMountPoint && printInstance.instance) {
                this.$refs.previewMountPoint.innerHTML = '';
                this.$refs.previewMountPoint.appendChild(printInstance.instance.$el);
            }
            this.printInstance = printInstance;
        },
        async updateInstanceGlobalConfig(newPostData) {
            if (this.currentExampleData.organ) {
                if (this._medicineTag) {
                    this.currentExampleData.organ.name = newPostData[newPostData.selectedOption].title;
                    this.currentExampleData.organ.subname = newPostData[newPostData.selectedOption].subtitle;
                } else {
                    this.currentExampleData.organ.name = newPostData.header ? newPostData.header.title : newPostData.title;
                    this.currentExampleData.organ.subname = newPostData.header ? newPostData.header.subtitle : newPostData.subtitle;
                }
            }

            if (this.currentExampleData?.rows?.[0]?.organPrintView) {
                if (this._medicineTag) {
                    this.currentExampleData.rows[0].organPrintView.name = newPostData[newPostData.selectedOption].title;
                    this.currentExampleData.rows[0].organPrintView.subname = newPostData[newPostData.selectedOption].subtitle;
                } else {
                    this.currentExampleData.rows[0].organPrintView.name = newPostData.header ? newPostData.header.title : newPostData.title;
                    this.currentExampleData.rows[0].organPrintView.subname = newPostData.header ? newPostData.header.subtitle : newPostData.subtitle;
                }
            }

            await this.printInstance.updateInstanceGlobalConfig(this.instanceGlobalConfigHandler(newPostData));
            await this.printInstance.updateDatahandlerAndInstance();
        },
    },
};
