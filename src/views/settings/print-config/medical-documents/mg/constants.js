export const EXAMPLE_DATA = {
    'id': 'ffffffff0000000034f523a3254e8000',
    'goodsType': 3,
    'type': 2,
    'subType': 10,
    'patientId': 'fdafd967f9a64f96882ba20fcef4f0b8',
    'patientOrderId': 'ffffffff0000000034f523a2e237c000',
    'examinationApplySheetId': '3815995442878906369',
    'wardAreaId': null,
    'relationPatientOrderId': null,
    'chargeSheetId': 'ffffffff0000000034f523a309148006',
    'chargeFormItemId': 'ffffffff0000000034f523a309148008',
    'chargeSheetType': 2,
    'outpatientFormItemId': 'ffffffff0000000034f523a2e492c001',
    'adviceExecuteItemId': null,
    'peFormItemId': null,
    'peFormItemIds': null,
    'organPrintView': {
        'id': 'fff730ccc5ee45d783d82a85b8a0e52d',
        'name': '高新大原店',
        'contactPhone': '0851-8511132',
        'addressProvinceName': '北京市',
        'addressCityName': '北京市',
        'addressDistrictName': '东城区',
        'addressDetail': '',
        'logo': 'https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/clinic-usage/fff730ccc5ee45d783d82a85b8a0e52d/basic/pexels-nui-malama-169330637-30129937_WceMDShbJ8H4.jpg',
        'qrUrl': 'http://weixin.qq.com/q/02GrXTEhJyeUD10000007g',
        'category': '街道卫生院',
        'addressProvinceId': '110000',
        'addressCityId': '110100',
        'addressDistrictId': '110101',
        'hisType': 0,
        'medicalDocumentsTitle': {
            'prescription': '高新大原店',
            'medical': '高新大原店',
            'infusion': '高新大原店',
            'treatment': '高新大原店',
            'examination': '高新大原店',
            'illnessCert': '高新大原店',
            'inspection': '高新大原店',
        },
    },
    'examinationId': 'ffffffff0000000034f522d1b7434000',
    'barCode': 30129,
    'businessType': 10,
    'orderNo': 'JC20250328001',
    'status': 1,
    'sampleStatus': 10,
    'doctorId': '6e45706922a74966ab51e4ed1e604641',
    'doctor': {
        'id': '6e45706922a74966ab51e4ed1e604641',
        'name': '丁柱112',
        'mobile': '***********',
        'countryCode': '86',
        'status': 1,
        'created': '2018-01-09 10:11:38',
        'shortId': '939523477594734595',
        'namePy': 'dingzhu112|zhengzhu112',
        'namePyFirst': 'DZ112|ZZ112',
        'headImgUrl': 'https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/clinic-usage/ffffffff000000001ea0b8f80078c000/doctor/2_D4FOMhXaVkeq.png',
        'handSign': 'https://cis-images-dev.oss-cn-shanghai.aliyuncs.com/prescription-sign/hSD6cP7pcvCuM6xBfsK6Pj87ZfeAyBLE_1737015062815',
        'wechatOpenIdMp': '1',
        'wechatNickName': '刘喜',
        'wechatUnionId': 'o2VGt0-_RMKgynfy3e6aipz3G268',
        'hasPassword': 1,
        'wechatSubscribe': 1,
    },
    'sellerId': '6e45706922a74966ab51e4ed1e604641',
    'seller': {
        'id': '6e45706922a74966ab51e4ed1e604641',
        'name': '丁柱112',
        'mobile': '***********',
        'countryCode': '86',
        'status': 1,
        'created': '2018-01-09 10:11:38',
        'shortId': '939523477594734595',
        'namePy': 'dingzhu112|zhengzhu112',
        'namePyFirst': 'DZ112|ZZ112',
        'headImgUrl': 'https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/clinic-usage/ffffffff000000001ea0b8f80078c000/doctor/2_D4FOMhXaVkeq.png',
        'handSign': 'https://cis-images-dev.oss-cn-shanghai.aliyuncs.com/prescription-sign/hSD6cP7pcvCuM6xBfsK6Pj87ZfeAyBLE_1737015062815',
        'wechatOpenIdMp': '1',
        'wechatNickName': '刘喜',
        'wechatUnionId': 'o2VGt0-_RMKgynfy3e6aipz3G268',
        'hasPassword': 1,
        'wechatSubscribe': 1,
    },
    'doctorDepartmentId': '59140f8ecdeb4553ab570f710274e0ab',
    'doctorDepartmentName': '小儿外科诊室',
    'sellerDepartmentId': '59140f8ecdeb4553ab570f710274e0ab',
    'sellerDepartmentName': '小儿外科诊室',
    'executeDepartmentId': '',
    'executeDepartmentName': null,
    'attachments': [
        {
            'url': 'https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/clinic-usage/fff730ccc5ee45d783d82a85b8a0e52d/inspect/WechatIMG13_a6uCbys6vwUD.png',
            'fileName': 'WechatIMG13.png',
            'fileSize': 216610,
            'sort': 0,
        },
    ],
    'remark': null,
    'itemsValue': null,
    'deviceData': null,
    'preItemsValue': null,
    'examinationHistories': null,
    'created': '2025-03-28T09:30:01Z',
    'orderByDate': '2025-03-28T09:30:01Z',
    'lastModifiedBy': '6e45706922a74966ab51e4ed1e604641',
    'patientOrderNumber': '30526',
    'wardAreaName': null,
    'bedNumber': null,
    'testerId': '6e45706922a74966ab51e4ed1e604641',
    'testerName': '丁柱112',
    'tester': {
        'id': '6e45706922a74966ab51e4ed1e604641',
        'name': '丁柱112',
        'mobile': '***********',
        'countryCode': '86',
        'status': 1,
        'created': '2018-01-09 10:11:38',
        'shortId': '939523477594734595',
        'namePy': 'dingzhu112|zhengzhu112',
        'namePyFirst': 'DZ112|ZZ112',
        'headImgUrl': 'https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/clinic-usage/ffffffff000000001ea0b8f80078c000/doctor/2_D4FOMhXaVkeq.png',
        'handSign': 'https://cis-images-dev.oss-cn-shanghai.aliyuncs.com/prescription-sign/hSD6cP7pcvCuM6xBfsK6Pj87ZfeAyBLE_1737015062815',
        'wechatOpenIdMp': '1',
        'wechatNickName': '刘喜',
        'wechatUnionId': 'o2VGt0-_RMKgynfy3e6aipz3G268',
        'hasPassword': 1,
        'wechatSubscribe': 1,
    },
    'testTime': '2025-03-28T09:33:00Z',
    'sampleType': null,
    'checkerId': '6e45706922a74966ab51e4ed1e604641',
    'checkerName': '丁柱112',
    'checker': {
        'id': '6e45706922a74966ab51e4ed1e604641',
        'name': '丁柱112',
        'mobile': '***********',
        'countryCode': '86',
        'status': 1,
        'created': '2018-01-09 10:11:38',
        'shortId': '939523477594734595',
        'namePy': 'dingzhu112|zhengzhu112',
        'namePyFirst': 'DZ112|ZZ112',
        'headImgUrl': 'https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/clinic-usage/ffffffff000000001ea0b8f80078c000/doctor/2_D4FOMhXaVkeq.png',
        'handSign': 'https://cis-images-dev.oss-cn-shanghai.aliyuncs.com/prescription-sign/hSD6cP7pcvCuM6xBfsK6Pj87ZfeAyBLE_1737015062815',
        'wechatOpenIdMp': '1',
        'wechatNickName': '刘喜',
        'wechatUnionId': 'o2VGt0-_RMKgynfy3e6aipz3G268',
        'hasPassword': 1,
        'wechatSubscribe': 1,
    },
    'checkTime': '2025-03-28T09:34:00Z',
    'reportTime': '2025-03-28T09:34:00Z',
    'chargeFormItemStatus': 0,
    'chargeFormItemOnceFee': 0,
    'examinationChargeFormItems': [
        {
            'examinationSheetId': 'ffffffff0000000034f523a3254e8000',
            'chargeFormItemSourceId': 'ffffffff0000000034f523a2e492c001',
            'chargeFormItemId': 'ffffffff0000000034f523a309148008',
            'chargeFormItemStatus': 0,
            'receivedUnitCount': 1,
            'receivedTotalPrice': 0,
            'chargeFormItemStatusName': '未收费',
            'receivedUnitPrice': 0,
        },
    ],
    'canExecute': 1,
    'diagnosis': null,
    'patient': {
        'id': 'fdafd967f9a64f96882ba20fcef4f0b8',
        'name': '王琴',
        'namePy': 'wangqin',
        'namePyFirst': 'WQ',
        'mobile': '13200000000',
        'countryCode': '86',
        'sex': '女',
        'birthday': '1991-05-05',
        'age': {
            'year': 33,
            'month': 10,
            'day': 23,
        },
        'isMember': 0,
        'idCardType': '身份证',
        'idCard': '',
        'marital': null,
        'weight': null,
        'importFlag': 0,
        'ethnicity': '',
        'nationality': null,
        'contactName': null,
        'contactRelation': null,
        'contactMobile': null,
        'sn': '261385',
        'remark': '',
        'profession': '',
        'company': '',
        'companyMobile': null,
        'blockFlag': 0,
        'address': {
            'addressCityId': null,
            'addressCityName': null,
            'addressDetail': '13',
            'addressDistrictId': null,
            'addressDistrictName': null,
            'addressGeo': null,
            'addressProvinceId': null,
            'addressProvinceName': null,
            'addressPostcode': null,
            'fullAddress': '13',
        },
        'familyMobile': null,
        'tags': null,
        'activeDate': '2025-03-28T09:30:01.000+00:00',
        'activeClinicId': 'fff730ccc5ee45d783d82a85b8a0e52d',
        'lastOutpatientDate': '2025-03-28T09:30:00.000+00:00',
        'lastOutpatientClinicId': 'fff730ccc5ee45d783d82a85b8a0e52d',
        'lastOutpatientDoctorId': '6e45706922a74966ab51e4ed1e604641',
        'wxOpenId': null,
        'unionId': null,
        'wxUserId': null,
        'wxNickName': null,
        'wxHeadImgUrl': null,
        'isWxMainPatient': 0,
        'wxBindStatus': 0,
        'patientSource': {
            'parentId': null,
            'parentName': null,
            'id': '11413126450807626510',
            'name': '转诊医生',
            'sourceFrom': '07b47b860f9649febc9d907e2ec6f23f',
            'sourceFromName': '蒲江琼',
            'relatedType': 1,
            'relatedId': null,
        },
        'allergicHistory': '否认药物过敏史，否认食物过敏史，左氧氟沙星过敏，林可霉素过敏，安定过敏',
    },
    'registrationFormItem': null,
    'examinationSheetReport': {
        'id': 'ffffffff0000000034f523c3454e8000',
        'examinationSheetId': 'ffffffff0000000034f523a3254e8000',
        'mergeSheetId': '3815995442878906371',
        'goodsType': 3,
        'type': 2,
        'subType': 10,
        'deviceType': 25,
        'imageFiles': [
            {
                'url': 'https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/clinic-usage/fff730ccc5ee45d783d82a85b8a0e52d/inspect/pexels-nui-malama-169330637-30129937_tFFC3n5zJQpv.jpg',
                'fileName': 'pexels-nui-malama-169330637-30129937.jpg',
                'fileSize': null,
                'name': '',
            },
        ],
        'method': null,
        'videoDescription': 'MG影像表现',
        'resultInfo': null,
        'advice': null,
        'suggestion': '',
        'diagnosisFlag': null,
        'versionFlag': 0,
        'recordDoctorId': '6e45706922a74966ab51e4ed1e604641',
        'recordDoctor': {
            'id': '6e45706922a74966ab51e4ed1e604641',
            'name': '丁柱112',
            'mobile': '***********',
            'countryCode': '86',
            'status': 1,
            'created': '2018-01-09 10:11:38',
            'shortId': '939523477594734595',
            'namePy': 'dingzhu112|zhengzhu112',
            'namePyFirst': 'DZ112|ZZ112',
            'headImgUrl': 'https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/clinic-usage/ffffffff000000001ea0b8f80078c000/doctor/2_D4FOMhXaVkeq.png',
            'handSign': 'https://cis-images-dev.oss-cn-shanghai.aliyuncs.com/prescription-sign/hSD6cP7pcvCuM6xBfsK6Pj87ZfeAyBLE_1737015062815',
            'wechatOpenIdMp': '1',
            'wechatNickName': '刘喜',
            'wechatUnionId': 'o2VGt0-_RMKgynfy3e6aipz3G268',
            'hasPassword': 1,
            'wechatSubscribe': 1,
        },
        'consultationDoctorId': '',
        'principalDoctorId': null,
        'examinationName': 'MG检查',
        'diagnosisEntryItems': [
            {
                'id': '3815995580854730753',
                'diagnosisEntryId': '3815994652604923904',
                'type': 2,
                'deviceType': 25,
                'name': 'MG诊断意见',
                'abnormalFlag': 10,
            },
        ],
        'principalDoctorName': null,
        'principalDoctorMobile': null,
        'recordDoctorName': '丁柱112',
        'consultationDoctorName': null,
    },
    'samplePipe': null,
    'deviceModelId': null,
    'deviceRoomId': null,
    'deviceView': {
        'id': '3779579454557454336',
        'deviceModelId': '3779579454557454336',
        'name': '检查仪器-111',
        'model': 'mask-111',
        'deviceUuid': 'mask-1-10',
        'manufacture': '三诺',
        'iconUrl': 'https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/oa/lis-device/image/6yljbPIQwY9SuLnrZOO1rCano4VqExEh_1675855625086.jpg',
        'goodsType': 3,
        'goodsSubType': 2,
        'goodsExtendSpec': '10',
        'deviceType': 25,
        'deviceTypeName': 'CT',
        'usageType': 0,
        'usageTypeName': '未知分类',
        'innerFlag': 0,
        'deviceExtensions': {
            'aeTitle': 'CT001',
        },
        'sampleNoRule': null,
        'connectStatus': 0,
        'deviceModelStatus': 0,
        'deviceId': '3788987003351744512',
        'deviceShortId': '000041',
        'deviceStatus': 10,
        'deviceStatusName': '使用中',
        'deviceRoomId': '3785417826818605056',
        'deviceRoomName': 'CT机房1',
        'department': {
            'id': 'ffffffff0000000034692fc6d5df0000',
            'name': '麻醉科',
            'departmentAddress': null,
            'tagId': 'ffffffff0000000034692fc6d5df0001',
            'type': 1,
            'isDefault': 0,
            'mobile': '',
            'beds': null,
            'customId': 'MZK0008',
            'isClinical': 1,
            'mainMedical': 'f9eb8dbc-a44a-11e9-99a1-acde48001122',
            'mainMedicalName': '麻醉科',
            'mainMedicalCode': '26',
        },
        'extendInfos': {},
    },
    'examinationApplySheetView': {
        'id': '3815995442878906369',
        'chainId': '6a869c22abee4ffbaef3e527bbb70aeb',
        'clinicId': 'fff730ccc5ee45d783d82a85b8a0e52d',
        'doctorId': '6e45706922a74966ab51e4ed1e604641',
        'departmentId': '59140f8ecdeb4553ab570f710274e0ab',
        'patientId': 'fdafd967f9a64f96882ba20fcef4f0b8',
        'patientOrderId': 'ffffffff0000000034f523a2e237c000',
        'registrationFormItemId': null,
        'deviceId': null,
        'deviceRoomId': null,
        'no': 'MG2503280001',
        'businessType': 20,
        'goodsType': 3,
        'type': 2,
        'subType': 10,
        'deviceType': 25,
        'chiefComplaint': '咳嗽',
        'presentHistory': null,
        'physicalExamination': null,
        'diagnosisInfos': [
            {
                'id': null,
                'diseaseName': '感冒病',
                'diseaseCode': 'BNW010',
            },
        ],
        'purpose': null,
        'planExecuteDate': '2025-03-28',
        'created': '2025-03-28T09:30:01Z',
        'status': 20,
        'patient': null,
        'dcm4cheeView': null,
        'pharmacyType': 0,
        'pharmacyNo': 0,
        'coChainId': null,
        'coClinicId': null,
        'doctorName': null,
    },
    'location': null,
    'peSheetSimpleView': null,
    'deviceType': 25,
    'isMerge': 0,
    'importFlag': 0,
    'updateItemsValueFlag': 1,
    'reportInvalidFlag': 1,
    'isMutualRecognition': 0,
    'sellerName': '丁柱112',
    'departmentName': '小儿外科诊室',
    'departmentId': '59140f8ecdeb4553ab570f710274e0ab',
    'innerFlag': 0,
    'goodsSubType': 2,
    'examinationApplySheetNo': 'MG2503280001',
    'doctorName': '丁柱112',
    'deviceStatus': null,
    'clinicPrintName': '高新大原店',
    'deviceName': null,
    'lastModifiedMillsTime': '1743154475000',
    'lastModifiedTime': '2025-03-28T09:34:35Z',
    'name': 'MG检查',
    'modifier': {
        'id': '6e45706922a74966ab51e4ed1e604641',
        'name': '丁柱112',
        'mobile': '***********',
        'countryCode': '86',
        'status': 1,
        'created': '2018-01-09 10:11:38',
        'shortId': '939523477594734595',
        'namePy': 'dingzhu112|zhengzhu112',
        'namePyFirst': 'DZ112|ZZ112',
        'headImgUrl': 'https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/clinic-usage/ffffffff000000001ea0b8f80078c000/doctor/2_D4FOMhXaVkeq.png',
        'handSign': 'https://cis-images-dev.oss-cn-shanghai.aliyuncs.com/prescription-sign/hSD6cP7pcvCuM6xBfsK6Pj87ZfeAyBLE_1737015062815',
        'wechatOpenIdMp': '1',
        'wechatNickName': '刘喜',
        'wechatUnionId': 'o2VGt0-_RMKgynfy3e6aipz3G268',
        'hasPassword': 1,
        'wechatSubscribe': 1,
    },
    'extendDiagnosisInfos': [
        {
            'toothNos': null,
            'value': [
                {
                    'code': 'BNW010',
                    'name': '感冒病',
                    'diseaseType': null,
                    'hint': '中医疾病',
                },
            ],
        },
    ],
    'modifierName': '丁柱112',
};
